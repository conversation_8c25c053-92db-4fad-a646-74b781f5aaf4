/** @type {import('tailwindcss').Config} */
export const content = [
  './Barret.Web.Server/**/*.{razor,html,cshtml}',
];
export const theme = {
  extend: {
    colors: {
      primary: '#008080',
      // Enhanced gray palette for better consistency
      gray: {
        50: '#f9fafb',
        100: '#f3f4f6',
        200: '#e5e7eb',
        300: '#d1d5db',
        400: '#9ca3af',
        500: '#6b7280',
        600: '#4b5563',
        700: '#374151',
        800: '#1f2937',
        900: '#111827',
      },
      // Success colors for positive actions
      success: {
        50: '#f0fdf4',
        100: '#dcfce7',
        200: '#bbf7d0',
        300: '#86efac',
        400: '#4ade80',
        500: '#22c55e',
        600: '#16a34a',
        700: '#15803d',
        800: '#166534',
        900: '#14532d',
      },
      // Warning colors for caution states
      warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f',
      },
      // Danger colors for destructive actions
      danger: {
        50: '#fef2f2',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d',
      },
    },
    borderRadius: {
      'xl': '0.75rem',
      '2xl': '1rem',
    },
    maxWidth: {
      '4xl': '56rem',
      '5xl': '64rem',
      '6xl': '72rem',
      '7xl': '80rem',
      '[1200px]': '1200px',
      '[1400px]': '1400px',
    },
    boxShadow: {
      'barret': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      'barret-md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      'barret-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      'barret-hover': '0 3px 6px -2px rgba(0,0,0,0.12), 0 4px 8px -1px rgba(0,0,0,0.07), 0 8px 16px -4px rgba(0,0,0,0.1)',
      'custom': '0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
      'custom-hover': '0 5px 10px -1px rgba(0, 0, 0, 0.2), 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 15px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 15px -5px rgba(0, 0, 0, 0.15)',
      'vehicle-card': '0 3px 5px -1px rgba(0,0,0,0.06), 0 2px 4px -1px rgba(0,0,0,0.04), 0 8px 10px -4px rgba(0,0,0,0.08)',
      'vehicle-group': '0 8px 15px -1px rgba(0, 0, 0, 0.25), 0 6px 10px -1px rgba(0, 0, 0, 0.2), 0 20px 30px -5px rgba(0, 0, 0, 0.25), 0 15px 20px -5px rgba(0, 0, 0, 0.2)',
    },
    animation: {
      'fade-in': 'fadeIn 0.3s ease-in-out',
      'slide-up': 'slideUp 0.3s ease-out',
      'slide-down': 'slideDown 0.3s ease-out',
    },
    keyframes: {
      fadeIn: {
        '0%': { opacity: '0' },
        '100%': { opacity: '1' },
      },
      slideUp: {
        '0%': { transform: 'translateY(10px)', opacity: '0' },
        '100%': { transform: 'translateY(0)', opacity: '1' },
      },
      slideDown: {
        '0%': { transform: 'translateY(-10px)', opacity: '0' },
        '100%': { transform: 'translateY(0)', opacity: '1' },
      },
    },
  },
};
export const plugins = [
  function({ addComponents, theme }) {
    addComponents({
      // Barret Button Base Classes
      '.barret-btn': {
        '@apply transition-all duration-200 ease-in-out font-medium': {},
      },

      // Button Sizes
      '.barret-btn-sm': {
        '@apply px-3 py-1.5 text-sm': {},
      },
      '.barret-btn-md': {
        '@apply px-4 py-2 text-base': {},
      },
      '.barret-btn-lg': {
        '@apply px-6 py-3 text-lg': {},
      },

      // Button Spacing Utilities
      '.barret-btn-group': {
        '@apply flex gap-2': {},
      },
      '.barret-btn-group-sm': {
        '@apply flex gap-1': {},
      },
      '.barret-btn-group-lg': {
        '@apply flex gap-3': {},
      },

      // Action Button Spacing (for grid actions)
      '.barret-action-btn': {
        '@apply px-2 py-1': {},
      },

      // Form Button Spacing (for form footers)
      '.barret-form-btn': {
        '@apply px-4 py-2': {},
      },

      // Header Button Spacing (for page headers)
      '.barret-header-btn': {
        '@apply px-6 py-3': {},
      },

      // Barret Form Component Base Classes
      '.barret-input': {
        '@apply transition-all duration-200 ease-in-out': {},
      },

      // Form Input Classes
      '.barret-form-input': {
        '@apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500': {},
      },
      '.barret-form-input-sm': {
        '@apply w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500': {},
      },
      '.barret-form-input-lg': {
        '@apply w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500': {},
      },

      // Form Select/Dropdown Classes
      '.barret-form-select': {
        '@apply w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white': {},
      },
      '.barret-form-select-sm': {
        '@apply w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white': {},
      },
      '.barret-form-select-lg': {
        '@apply w-full px-4 py-3 text-lg border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white': {},
      },

      // Form Checkbox Classes
      '.barret-form-checkbox': {
        '@apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded': {},
      },
      '.barret-form-checkbox-sm': {
        '@apply h-3 w-3 text-primary-600 focus:ring-primary-500 border-gray-300 rounded': {},
      },
      '.barret-form-checkbox-lg': {
        '@apply h-5 w-5 text-primary-600 focus:ring-primary-500 border-gray-300 rounded': {},
      },

      // Form Group Classes
      '.barret-form-group': {
        '@apply mb-4': {},
      },
      '.barret-form-group-sm': {
        '@apply mb-2': {},
      },
      '.barret-form-group-lg': {
        '@apply mb-6': {},
      },

      // Form Field Container Classes
      '.barret-form-field': {
        '@apply space-y-1': {},
      },
      '.barret-form-field-inline': {
        '@apply flex items-center space-x-3': {},
      },

      // Barret Data Grid Classes
      '.barret-grid': {
        '@apply w-full border border-gray-300 rounded-md overflow-hidden': {},
      },
      '.barret-grid-sm': {
        '@apply w-full border border-gray-300 rounded text-sm': {},
      },
      '.barret-grid-lg': {
        '@apply w-full border border-gray-300 rounded-lg text-lg': {},
      },

      // Grid Header Classes
      '.barret-grid-header': {
        '@apply bg-gray-50 font-semibold text-gray-700 border-b border-gray-200': {},
      },
      '.barret-grid-header-sm': {
        '@apply bg-gray-50 font-medium text-gray-700 text-sm border-b border-gray-200': {},
      },
      '.barret-grid-header-lg': {
        '@apply bg-gray-50 font-bold text-gray-700 text-lg border-b border-gray-200': {},
      },

      // Grid Row Classes
      '.barret-grid-row': {
        '@apply hover:bg-gray-50 transition-colors duration-150': {},
      },
      '.barret-grid-row-selected': {
        '@apply bg-primary-50 border-primary-200': {},
      },
      '.barret-grid-row-striped': {
        '@apply even:bg-gray-50': {},
      },

      // Grid Cell Classes
      '.barret-grid-cell': {
        '@apply px-4 py-3 border-b border-gray-100': {},
      },
      '.barret-grid-cell-sm': {
        '@apply px-2 py-2 text-sm border-b border-gray-100': {},
      },
      '.barret-grid-cell-lg': {
        '@apply px-6 py-4 text-lg border-b border-gray-100': {},
      },

      // Grid Action Classes
      '.barret-grid-actions': {
        '@apply flex items-center space-x-2 justify-center': {},
      },
      '.barret-grid-actions-left': {
        '@apply flex items-center space-x-2 justify-start': {},
      },
      '.barret-grid-actions-right': {
        '@apply flex items-center space-x-2 justify-end': {},
      },
    })
  }
];
export const safelist = [
  // Add classes that might be used dynamically
  'bg-white',
  'bg-gray-50',
  'bg-gray-100',
  'bg-gray-200',
  'bg-gray-800',
  'bg-gray-900',
  'text-white',
  'text-gray-500',
  'text-gray-700',
  'text-gray-900',
  'hover:bg-gray-800',
  'hover:shadow-md',
  'border-0',
  'border-gray-100',
  'border-gray-200',
  'rounded-xl',
  'rounded-full',
  'shadow-sm',
  'transition-shadow',
  'transition-colors',
  'duration-300',
  'h-full',
  'h-8',
  'h-12',
  'h-16',
  'w-8',
  'w-12',
  'w-16',
  'animate-spin',
  'border-t-gray-800',
  'border-4',
  'text-sm',
  'text-base',
  'text-lg',
  'text-xl',
  'text-2xl',
  'text-3xl',
  'font-normal',
  'font-medium',
  'max-w-[1200px]',
  'max-w-4xl',
  'grid-cols-1',
  'md:grid-cols-2',
  'lg:grid-cols-4',
  'gap-6',
  'mb-2',
  'mb-6',
  'mb-8',
  'md:mb-16',
  'mt-2',
  'mt-4',
  'mt-auto',
  'px-4',
  'px-6',
  'py-2',
  'py-8',
  'p-8',
  'flex-1',
  'flex-col',
  'items-center',
  'justify-center',
  'text-center',
  'overflow-hidden',
  'cursor-pointer',
  // Barret Button Classes
  'barret-btn',
  'barret-btn-sm',
  'barret-btn-md',
  'barret-btn-lg',
  'barret-btn-group',
  'barret-btn-group-sm',
  'barret-btn-group-lg',
  'barret-action-btn',
  'barret-form-btn',
  'barret-header-btn',
  // Barret Form Component Classes
  'barret-input',
  'barret-form-input',
  'barret-form-input-sm',
  'barret-form-input-lg',
  'barret-form-select',
  'barret-form-select-sm',
  'barret-form-select-lg',
  'barret-form-checkbox',
  'barret-form-checkbox-sm',
  'barret-form-checkbox-lg',
  'barret-form-group',
  'barret-form-group-sm',
  'barret-form-group-lg',
  'barret-form-field',
  'barret-form-field-inline',
  // Barret Data Grid Classes
  'barret-grid',
  'barret-grid-sm',
  'barret-grid-lg',
  'barret-grid-header',
  'barret-grid-header-sm',
  'barret-grid-header-lg',
  'barret-grid-row',
  'barret-grid-row-selected',
  'barret-grid-row-striped',
  'barret-grid-cell',
  'barret-grid-cell-sm',
  'barret-grid-cell-lg',
  'barret-grid-actions',
  'barret-grid-actions-left',
  'barret-grid-actions-right',
];
