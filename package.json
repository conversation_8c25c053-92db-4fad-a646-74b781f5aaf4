{"name": "barret-vehicle-configurator", "version": "1.0.0", "description": "Barret Vehicle Configurator", "scripts": {"build:css": "npm run build:tailwind && npm run build:radzen", "build:css:dev": "npm run build:tailwind:dev && npm run build:radzen:dev", "watch:css": "npm-run-all --parallel watch:tailwind watch:radzen", "watch:css:poll": "npm-run-all --parallel watch:tailwind:poll watch:radzen:poll", "purge:css": "npm run build:tailwind:purge && npm run build:radzen", "analyze:css": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --content './Barret.Web.Server/**/*.{razor,html,cshtml}' --verbose", "build:tailwind": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --minify", "build:tailwind:dev": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css", "build:tailwind:purge": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --minify --purge", "watch:tailwind": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --watch", "watch:tailwind:poll": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --watch --poll", "build:radzen": "npm run build:radzen:software && npm run build:radzen:software-dark", "build:radzen:dev": "npm run build:radzen:software:dev && npm run build:radzen:software-dark:dev", "build:radzen:software": "sass ./Barret.Web.Server/wwwroot/css/themes/software.scss ./Barret.Web.Server/wwwroot/css/software.css --style=compressed --no-source-map", "build:radzen:software:dev": "sass ./Barret.Web.Server/wwwroot/css/themes/software.scss ./Barret.Web.Server/wwwroot/css/software.css --style=expanded --source-map", "build:radzen:software-dark": "sass ./Barret.Web.Server/wwwroot/css/themes/software-dark.scss ./Barret.Web.Server/wwwroot/css/software-dark.css --style=compressed --no-source-map", "build:radzen:software-dark:dev": "sass ./Barret.Web.Server/wwwroot/css/themes/software-dark.scss ./Barret.Web.Server/wwwroot/css/software-dark.css --style=expanded --source-map", "watch:radzen": "npm-run-all --parallel watch:radzen:software watch:radzen:software-dark", "watch:radzen:poll": "npm-run-all --parallel watch:radzen:software:poll watch:radzen:software-dark:poll", "watch:radzen:software": "sass ./Barret.Web.Server/wwwroot/css/themes/software.scss ./Barret.Web.Server/wwwroot/css/software.css --watch --style=expanded --source-map", "watch:radzen:software:poll": "sass ./Barret.Web.Server/wwwroot/css/themes/software.scss ./Barret.Web.Server/wwwroot/css/software.css --watch --poll --style=expanded --source-map", "watch:radzen:software-dark": "sass ./Barret.Web.Server/wwwroot/css/themes/software-dark.scss ./Barret.Web.Server/wwwroot/css/software-dark.css --watch --style=expanded --source-map", "watch:radzen:software-dark:poll": "sass ./Barret.Web.Server/wwwroot/css/themes/software-dark.scss ./Barret.Web.Server/wwwroot/css/software-dark.css --watch --poll --style=expanded --source-map"}, "dependencies": {"tailwindcss": "^3.4.17"}, "devDependencies": {"sass": "^1.80.7", "npm-run-all": "^4.1.5"}}