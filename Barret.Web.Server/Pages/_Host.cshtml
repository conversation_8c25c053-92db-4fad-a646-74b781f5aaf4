@page "/"
@using Microsoft.AspNetCore.Components.Web
@namespace Barret.Web.Server.Pages
@addTagHelper *, Microsoft.AspNetCore.Mvc.TagHelpers

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="~/" />

    <!-- jQuery first -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap CSS with lower specificity -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" media="print" onload="this.media='all'" />



    <!-- Custom Radzen Theme CSS (compiled from SCSS) -->
    <link href="css/software.css" rel="stylesheet" />


    <!-- Application CSS - Load with higher specificity -->
    <link href="css/Styles/dist.css" rel="stylesheet" />
    <link href="css/Styles/device-editor.css" rel="stylesheet" />
    <link href="css/Styles/device-import.css" rel="stylesheet" />
    <link href="css/Styles/barret-grids.css" rel="stylesheet" />
    <link href="css/Styles/device-grid-tailwind.css" rel="stylesheet" />
    <link href="css/vehicle-card-shadow.css" rel="stylesheet" />
    <link href="css/vehicle-type-card-shadow.css" rel="stylesheet" />
    <link href="css/card-shadow.css" rel="stylesheet" />
    <link href="css/focus-styles.css" rel="stylesheet" />
    <link href="css/blazor-error-ui.css" rel="stylesheet" />

    <!-- Custom styles to ensure Tailwind has higher specificity -->
    <style>
        /* Vehicle card shadow effect */
        .vehicle-card-with-shadow {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            transition: box-shadow 0.5s ease !important;
            height: 100% !important;
            cursor: pointer !important;
        }

        .vehicle-card-with-shadow:hover {
            box-shadow:
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 4px 6px -1px rgba(0, 0, 0, 0.05),
                0 6px 8px -2px rgba(0, 0, 0, 0.04) !important;
        }

        /* Vehicle type card shadow effect */
        .vehicle-type-card-with-shadow {
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
            transition: box-shadow 0.5s ease !important;
            height: 100% !important;
            cursor: pointer !important;
        }

        .vehicle-type-card-with-shadow:hover {
            box-shadow:
                0 2px 4px -1px rgba(0, 0, 0, 0.06),
                0 4px 6px -1px rgba(0, 0, 0, 0.05),
                0 6px 8px -2px rgba(0, 0, 0, 0.04) !important;
        }

        .vehicle-type-card-with-shadow:hover .rounded-full {
            background-color: #1f2937 !important;
        }

        /* Remove focus outline from h1 elements */
        h1:focus { outline: none !important; }

        /* Ensure Tailwind classes have higher specificity */
        .min-h-screen { min-height: 100vh !important; }
        .bg-white { background-color: #ffffff !important; }
        .max-w-\[1400px\] { max-width: 1400px !important; }
        .mx-auto { margin-left: auto !important; margin-right: auto !important; }
        .px-6 { padding-left: 1.5rem !important; padding-right: 1.5rem !important; }
        .py-8 { padding-top: 2rem !important; padding-bottom: 2rem !important; }
        .mb-8 { margin-bottom: 2rem !important; }
        .flex { display: flex !important; }
        .items-center { align-items: center !important; }
        .justify-between { justify-content: space-between !important; }
        .mb-4 { margin-bottom: 1rem !important; }
        .text-2xl { font-size: 1.5rem !important; }
        .font-medium { font-weight: 500 !important; }
        .text-gray-900 { color: #111827 !important; }
        .gap-3 { gap: 0.75rem !important; }
        .h-10 { height: 2.5rem !important; }
        .px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
        .rounded-full { border-radius: 9999px !important; }
        .border { border-width: 1px !important; }
        .border-gray-200 { border-color: #e5e7eb !important; }
        .text-gray-700 { color: #374151 !important; }
        .hover\:bg-gray-50:hover { background-color: #f9fafb !important; }
        .hover\:text-gray-900:hover { color: #111827 !important; }
        .transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke !important; }
        .bg-gray-900 { background-color: #111827 !important; }
        .text-white { color: #ffffff !important; }
        .hover\:bg-gray-800:hover { background-color: #1f2937 !important; }
        .flex-col { flex-direction: column !important; }
        .sm\:flex-row { flex-direction: row !important; }
        .sm\:items-center { align-items: center !important; }
        .gap-4 { gap: 1rem !important; }
        .text-gray-500 { color: #6b7280 !important; }
        .relative { position: relative !important; }
        .w-full { width: 100% !important; }
        .sm\:w-64 { width: 16rem !important; }
        .md\:w-80 { width: 20rem !important; }
    </style>
    <link href="css/barret-theme.css" rel="stylesheet" />

    <!-- Icons -->
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
    <link
        href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp"
        rel="stylesheet">

    <component type="typeof(HeadOutlet)" render-mode="ServerPrerendered" />
</head>

<body>
    <component type="typeof(App)" render-mode="ServerPrerendered" />

    <div id="blazor-error-ui" style="display: none;">
        <environment include="Staging,Production">
            An error has occurred. This application may no longer respond until reloaded.
        </environment>
        @* <environment include="Development">
            An unhandled exception has occurred. See browser dev tools for details.
        </environment> *@
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.server.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    <script src="js/toastConfirmation.js"></script>
    <script src="js/toastService.js"></script>
    <script src="js/fileDownload.js"></script>
    <script src="js/fileUtils.js"></script>
    <script src="js/navigation-helpers.js"></script>
    <script src="js/vehicle-editor-new.js"></script>
    <script src="js/blazor-error-ui.js"></script>

    <script>
        window.storeInterfaceForEdit = function (device) {
            window.currentInterfaceToEdit = device;
        };

        window.openInterfaceEditModal = function () {
            // Use a setTimeout to ensure this runs after the current modal event processing is complete
            setTimeout(function () {
                DotNet.invokeMethodAsync('Barret.Web.Server', 'OpenInterfaceEditor', window.currentInterfaceToEdit);
            }, 100);
        };
    </script>
</body>

</html>
