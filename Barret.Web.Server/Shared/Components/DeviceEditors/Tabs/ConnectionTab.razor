@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Radzen.Blazor
@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs

<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
    <RadzenFormField Text="IP Address" Variant="Variant.Outlined">
        <RadzenTextBox Value="@Device.Connection.IPAddress"
                       ValueChanged="@OnIPAddressChanged"
                       Placeholder="Enter IP address"
                       class="@($"barret-input w-full {(RequireConnection && string.IsNullOrWhiteSpace(Device.Connection.IPAddress) ? "border-red-500" : "")}")" />
        @if (RequireConnection && string.IsNullOrWhiteSpace(Device.Connection.IPAddress))
        {
            <div class="text-red-600 text-sm mt-1">IP Address is required</div>
        }
    </RadzenFormField>

    <RadzenFormField Text="Port" Variant="Variant.Outlined">
        <RadzenNumeric Value="@Device.Connection.Port"
                       ValueChanged="@(EventCallback.Factory.Create<int>(this, OnPortChanged))"
                       Min="1"
                       Max="65535"
                       Placeholder="Enter port number"
                       class="@($"barret-input w-full {(RequireConnection && Device.Connection.Port <= 0 ? "border-red-500" : "")}")" />
        @if (RequireConnection && Device.Connection.Port <= 0)
        {
            <div class="text-red-600 text-sm mt-1">Port is required</div>
        }
    </RadzenFormField>

    <RadzenFormField Text="Protocol" Variant="Variant.Outlined">
        <RadzenDropDown Value="@Device.Connection.Protocol"
                        ValueChanged="@(EventCallback.Factory.Create<Protocol>(this, OnProtocolChanged))"
                        Data="@ProtocolOptions"
                        TextProperty="Text"
                        ValueProperty="Value"
                        Placeholder="Select protocol"
                        class="@($"barret-input w-full {(RequireConnection && Device.Connection.Protocol == Protocol.Undefined ? "border-red-500" : "")}")" />
        @if (RequireConnection && Device.Connection.Protocol == Protocol.Undefined)
        {
            <div class="text-red-600 text-sm mt-1">Protocol is required</div>
        }
    </RadzenFormField>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public bool RequireConnection { get; set; } = false;

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Protocol options for RadzenDropDown
    private List<ProtocolOption> ProtocolOptions { get; set; } = new();

    protected override void OnInitialized()
    {
        ProtocolOptions = Enum.GetValues(typeof(Protocol))
            .Cast<Protocol>()
            .Select(p => new ProtocolOption { Text = p.ToString(), Value = p })
            .ToList();
    }

    private async Task OnIPAddressChanged(string newValue)
    {
        Device.Connection.IPAddress = newValue;
        await NotifyPropertyChanged();
    }

    private async Task OnPortChanged(int newValue)
    {
        Device.Connection.Port = newValue;
        await NotifyPropertyChanged();
    }

    private async Task OnProtocolChanged(Protocol newValue)
    {
        Device.Connection.Protocol = newValue;
        await NotifyPropertyChanged();
    }

    private async Task NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            await OnPropertyChanged.InvokeAsync();
        }
    }

    private class ProtocolOption
    {
        public string Text { get; set; } = string.Empty;
        public Protocol Value { get; set; }
    }
}
