@import url("https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap");
@font-face {
  font-family: "Material Symbols Outlined";
  font-style: normal;
  font-weight: 100 700;
  font-display: block;
  src: url("../fonts/MaterialSymbolsOutlined.woff2") format("woff2");
}
@font-face {
  font-family: "Roboto Flex";
  font-style: normal;
  font-weight: 100 1000;
  font-display: swap;
  src: url("../fonts/RobotoFlex.woff2") format("woff2");
}
@font-face {
  font-family: "Source Sans 3";
  font-style: normal;
  font-weight: 200 900;
  font-display: swap;
  src: url("../fonts/SourceSans3VF-Upright.ttf.woff2") format("woff2");
}
@font-face {
  font-family: "Source Sans 3";
  font-style: italic;
  font-weight: 200 900;
  font-display: swap;
  src: url("../fonts/SourceSans3VF-Italic.ttf.woff2") format("woff2");
}
:root {
  --rz-font-family: Source Sans Pro, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  --rz-font-size: 0.875rem;
  --rz-line-height: 1.429;
  --rz-icon-font-family: "Material Symbols Outlined";
}

body {
  font-family: var(--rz-font-family);
  font-size: var(--rz-font-size);
  line-height: var(--rz-line-height);
}

.rzi {
  font-family: var(--rz-icon-font-family);
  font-weight: normal;
  font-style: normal;
  font-size: var(--rz-icon-size);
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

:root,
.rz-software-dark {
  --rz-white: #ffffff;
}

:root,
.rz-software-dark {
  --rz-black: #000000;
}

:root,
.rz-software-dark {
  --rz-base: #111827;
}

:root,
.rz-software-dark {
  --rz-base-50: #111827;
}

:root,
.rz-software-dark {
  --rz-base-100: #1f2937;
}

:root,
.rz-software-dark {
  --rz-base-200: #374151;
}

:root,
.rz-software-dark {
  --rz-base-300: #4b5563;
}

:root,
.rz-software-dark {
  --rz-base-400: #6b7280;
}

:root,
.rz-software-dark {
  --rz-base-500: #9ca3af;
}

:root,
.rz-software-dark {
  --rz-base-600: #d1d5db;
}

:root,
.rz-software-dark {
  --rz-base-700: #e5e7eb;
}

:root,
.rz-software-dark {
  --rz-base-800: #f3f4f6;
}

:root,
.rz-software-dark {
  --rz-base-900: #ffffff;
}

:root,
.rz-software-dark {
  --rz-base-light: #374151;
}

:root,
.rz-software-dark {
  --rz-base-lighter: #111827;
}

:root,
.rz-software-dark {
  --rz-base-dark: #e5e7eb;
}

:root,
.rz-software-dark {
  --rz-base-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-primary: #ffffff;
}

:root,
.rz-software-dark {
  --rz-primary-light: white;
}

:root,
.rz-software-dark {
  --rz-primary-lighter: rgba(255, 255, 255, 0.16);
}

:root,
.rz-software-dark {
  --rz-primary-dark: rgb(234.6, 234.6, 234.6);
}

:root,
.rz-software-dark {
  --rz-primary-darker: rgb(191.25, 191.25, 191.25);
}

:root,
.rz-software-dark {
  --rz-secondary: #1f2937;
}

:root,
.rz-software-dark {
  --rz-secondary-light: rgb(57.88, 66.68, 79);
}

:root,
.rz-software-dark {
  --rz-secondary-lighter: rgba(31, 41, 55, 0.2);
}

:root,
.rz-software-dark {
  --rz-secondary-dark: rgb(28.52, 37.72, 50.6);
}

:root,
.rz-software-dark {
  --rz-secondary-darker: rgb(23.25, 30.75, 41.25);
}

:root,
.rz-software-dark {
  --rz-info: #9ca3af;
}

:root,
.rz-software-dark {
  --rz-info-light: rgb(171.84, 177.72, 187.8);
}

:root,
.rz-software-dark {
  --rz-info-lighter: rgba(156, 163, 175, 0.2);
}

:root,
.rz-software-dark {
  --rz-info-dark: rgb(131.04, 136.92, 147);
}

:root,
.rz-software-dark {
  --rz-info-darker: rgb(117, 122.25, 131.25);
}

:root,
.rz-software-dark {
  --rz-success: #ffffff;
}

:root,
.rz-software-dark {
  --rz-success-light: white;
}

:root,
.rz-software-dark {
  --rz-success-lighter: rgba(255, 255, 255, 0.16);
}

:root,
.rz-software-dark {
  --rz-success-dark: rgb(214.2, 214.2, 214.2);
}

:root,
.rz-software-dark {
  --rz-success-darker: rgb(191.25, 191.25, 191.25);
}

:root,
.rz-software-dark {
  --rz-warning: #d1d5db;
}

:root,
.rz-software-dark {
  --rz-warning-light: rgb(216.36, 219.72, 224.76);
}

:root,
.rz-software-dark {
  --rz-warning-lighter: rgba(209, 213, 219, 0.2);
}

:root,
.rz-software-dark {
  --rz-warning-dark: rgb(175.56, 178.92, 183.96);
}

:root,
.rz-software-dark {
  --rz-warning-darker: rgb(156.75, 159.75, 164.25);
}

:root,
.rz-software-dark {
  --rz-danger: #ffffff;
}

:root,
.rz-software-dark {
  --rz-danger-light: white;
}

:root,
.rz-software-dark {
  --rz-danger-lighter: rgba(255, 255, 255, 0.2);
}

:root,
.rz-software-dark {
  --rz-danger-dark: rgb(214.2, 214.2, 214.2);
}

:root,
.rz-software-dark {
  --rz-danger-darker: rgb(191.25, 191.25, 191.25);
}

:root,
.rz-software-dark {
  --rz-on-base: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-base-light: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-base-lighter: #000000;
}

:root,
.rz-software-dark {
  --rz-on-base-dark: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-base-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-primary: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-primary-light: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-primary-lighter: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-primary-dark: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-primary-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-secondary: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-secondary-light: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-secondary-lighter: #1f2937;
}

:root,
.rz-software-dark {
  --rz-on-secondary-dark: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-secondary-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-info: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-info-light: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-info-lighter: #9ca3af;
}

:root,
.rz-software-dark {
  --rz-on-info-dark: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-info-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-success: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-success-light: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-success-lighter: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-success-dark: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-success-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-warning: #000000;
}

:root,
.rz-software-dark {
  --rz-on-warning-light: #000000;
}

:root,
.rz-software-dark {
  --rz-on-warning-lighter: #d1d5db;
}

:root,
.rz-software-dark {
  --rz-on-warning-dark: #000000;
}

:root,
.rz-software-dark {
  --rz-on-warning-darker: #000000;
}

:root,
.rz-software-dark {
  --rz-on-danger: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-danger-light: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-danger-lighter: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-danger-dark: #ffffff;
}

:root,
.rz-software-dark {
  --rz-on-danger-darker: #ffffff;
}

:root,
.rz-software-dark {
  --rz-text-color: var(--rz-base-50);
}

:root,
.rz-software-dark {
  --rz-text-contrast-color: var(--rz-base-50);
}

:root,
.rz-software-dark {
  --rz-border-width: 1px;
  --rz-border-radius: 4px;
  --rz-body-font-size: 0.875rem;
  --rz-body-line-height: 1.429;
  --rz-icon-size: 1rem;
  --rz-outline-offset: 2px;
  --rz-outline-width: 2px;
  --rz-outline-color: var(--rz-primary);
  --rz-outline-normal: none;
  --rz-outline-focus: 2px solid var(--rz-outline-color);
  --rz-transition-all: all 0.2s ease-in-out;
  --rz-button-base-background-color: var(--rz-primary);
  --rz-button-base-color: var(--rz-on-primary);
  --rz-button-border-radius: 4px;
  --rz-button-background-size: auto;
  --rz-button-shadow: none;
  --rz-button-transition: var(--rz-transition-all), width 0, height 0;
  --rz-button-line-height: 1.25rem;
  --rz-button-vertical-align: top;
  --rz-button-hover-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2);
  --rz-button-hover-gradient: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  --rz-button-hover-background-size: auto;
  --rz-button-focus-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2);
  --rz-button-focus-gradient: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  --rz-button-focus-background-size: auto;
  --rz-button-focus-outline: var(--rz-outline-focus);
  --rz-button-focus-outline-offset: var(--rz-outline-offset);
  --rz-button-active-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1);
  --rz-button-active-gradient: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
  --rz-button-active-background-size: auto;
  --rz-button-disabled-opacity: 0.2;
  --rz-button-empty-opacity: 0.4;
  --rz-grid-simple-filter-icon-active-color: var(--rz-secondary-darker);
  --rz-grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter);
}

body {
  background-color: var(--rz-body-background-color, var(--rz-base-100));
  color: var(--rz-text-color, var(--rz-on-base));
  font-family: var(--rz-font-family);
  font-size: var(--rz-body-font-size);
  line-height: var(--rz-body-line-height);
}

.rz-button {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  color: var(--rz-button-base-color);
  background-color: var(--rz-button-base-background-color);
  background-size: var(--rz-button-background-size);
  background-repeat: no-repeat;
  border: none;
  border-radius: var(--rz-button-border-radius);
  outline: none;
  box-shadow: var(--rz-button-shadow);
  font-size: 1.0625rem;
  font-family: inherit;
  line-height: var(--rz-button-line-height);
  text-decoration: none;
  transition: var(--rz-button-transition);
  -webkit-appearance: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
}
.rz-button:focus {
  outline: var(--rz-outline-normal);
}
.rz-button:focus-visible {
  outline: var(--rz-button-focus-outline);
  outline-offset: var(--rz-button-focus-outline-offset);
}
.rz-button:not(.rz-state-disabled):not(:active):hover {
  text-decoration: none;
  background-image: var(--rz-button-hover-gradient);
  background-size: var(--rz-button-hover-background-size);
  box-shadow: var(--rz-button-hover-shadow);
}
.rz-button:not(.rz-state-disabled):not(:active):focus-visible {
  text-decoration: none;
  background-image: var(--rz-button-focus-gradient);
  background-size: var(--rz-button-focus-background-size);
  box-shadow: var(--rz-button-focus-shadow);
}
.rz-button:not(.rz-state-disabled):active {
  text-decoration: none;
  background-image: var(--rz-button-active-gradient);
  background-size: var(--rz-button-active-background-size);
  box-shadow: var(--rz-button-active-shadow);
}
.rz-button.rz-state-disabled {
  opacity: var(--rz-button-disabled-opacity);
  cursor: initial;
}
.rz-button .rz-button-box {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  vertical-align: var(--rz-button-vertical-align);
  line-height: var(--rz-button-line-height);
}
.rz-button .rz-button-text {
  vertical-align: var(--rz-button-vertical-align);
}
.rz-button .rzi {
  vertical-align: var(--rz-button-vertical-align);
}

.rz-button.rz-primary {
  background-color: var(--rz-primary);
  color: var(--rz-on-primary);
}
.rz-button.rz-primary.rz-shade-lighter {
  background-color: var(--rz-primary-lighter);
  color: var(--rz-on-primary-lighter);
}
.rz-button.rz-primary.rz-shade-light {
  background-color: var(--rz-primary-light);
  color: var(--rz-on-primary-light);
}
.rz-button.rz-primary.rz-shade-default {
  background-color: var(--rz-primary);
  color: var(--rz-on-primary);
}
.rz-button.rz-primary.rz-shade-dark {
  background-color: var(--rz-primary-dark);
  color: var(--rz-on-primary-dark);
}
.rz-button.rz-primary.rz-shade-darker {
  background-color: var(--rz-primary-darker);
  color: var(--rz-on-primary-darker);
}

.rz-button.rz-secondary {
  background-color: var(--rz-secondary);
  color: var(--rz-on-secondary);
}
.rz-button.rz-secondary.rz-shade-lighter {
  background-color: var(--rz-secondary-lighter);
  color: var(--rz-on-secondary-lighter);
}
.rz-button.rz-secondary.rz-shade-light {
  background-color: var(--rz-secondary-light);
  color: var(--rz-on-secondary-light);
}
.rz-button.rz-secondary.rz-shade-default {
  background-color: var(--rz-secondary);
  color: var(--rz-on-secondary);
}
.rz-button.rz-secondary.rz-shade-dark {
  background-color: var(--rz-secondary-dark);
  color: var(--rz-on-secondary-dark);
}
.rz-button.rz-secondary.rz-shade-darker {
  background-color: var(--rz-secondary-darker);
  color: var(--rz-on-secondary-darker);
}

.rz-button.rz-info {
  background-color: var(--rz-info);
  color: var(--rz-on-info);
}
.rz-button.rz-info.rz-shade-lighter {
  background-color: var(--rz-info-lighter);
  color: var(--rz-on-info-lighter);
}
.rz-button.rz-info.rz-shade-light {
  background-color: var(--rz-info-light);
  color: var(--rz-on-info-light);
}
.rz-button.rz-info.rz-shade-default {
  background-color: var(--rz-info);
  color: var(--rz-on-info);
}
.rz-button.rz-info.rz-shade-dark {
  background-color: var(--rz-info-dark);
  color: var(--rz-on-info-dark);
}
.rz-button.rz-info.rz-shade-darker {
  background-color: var(--rz-info-darker);
  color: var(--rz-on-info-darker);
}

.rz-button.rz-success {
  background-color: var(--rz-success);
  color: var(--rz-on-success);
}
.rz-button.rz-success.rz-shade-lighter {
  background-color: var(--rz-success-lighter);
  color: var(--rz-on-success-lighter);
}
.rz-button.rz-success.rz-shade-light {
  background-color: var(--rz-success-light);
  color: var(--rz-on-success-light);
}
.rz-button.rz-success.rz-shade-default {
  background-color: var(--rz-success);
  color: var(--rz-on-success);
}
.rz-button.rz-success.rz-shade-dark {
  background-color: var(--rz-success-dark);
  color: var(--rz-on-success-dark);
}
.rz-button.rz-success.rz-shade-darker {
  background-color: var(--rz-success-darker);
  color: var(--rz-on-success-darker);
}

.rz-button.rz-warning {
  background-color: var(--rz-warning);
  color: var(--rz-on-warning);
}
.rz-button.rz-warning.rz-shade-lighter {
  background-color: var(--rz-warning-lighter);
  color: var(--rz-on-warning-lighter);
}
.rz-button.rz-warning.rz-shade-light {
  background-color: var(--rz-warning-light);
  color: var(--rz-on-warning-light);
}
.rz-button.rz-warning.rz-shade-default {
  background-color: var(--rz-warning);
  color: var(--rz-on-warning);
}
.rz-button.rz-warning.rz-shade-dark {
  background-color: var(--rz-warning-dark);
  color: var(--rz-on-warning-dark);
}
.rz-button.rz-warning.rz-shade-darker {
  background-color: var(--rz-warning-darker);
  color: var(--rz-on-warning-darker);
}

.rz-button.rz-danger {
  background-color: var(--rz-danger);
  color: var(--rz-on-danger);
}
.rz-button.rz-danger.rz-shade-lighter {
  background-color: var(--rz-danger-lighter);
  color: var(--rz-on-danger-lighter);
}
.rz-button.rz-danger.rz-shade-light {
  background-color: var(--rz-danger-light);
  color: var(--rz-on-danger-light);
}
.rz-button.rz-danger.rz-shade-default {
  background-color: var(--rz-danger);
  color: var(--rz-on-danger);
}
.rz-button.rz-danger.rz-shade-dark {
  background-color: var(--rz-danger-dark);
  color: var(--rz-on-danger-dark);
}
.rz-button.rz-danger.rz-shade-darker {
  background-color: var(--rz-danger-darker);
  color: var(--rz-on-danger-darker);
}

.rz-button.rz-base {
  background-color: var(--rz-base);
  color: var(--rz-on-base);
}
.rz-button.rz-base.rz-shade-lighter {
  background-color: var(--rz-base-lighter);
  color: var(--rz-on-base-lighter);
}
.rz-button.rz-base.rz-shade-light {
  background-color: var(--rz-base-light);
  color: var(--rz-on-base-light);
}
.rz-button.rz-base.rz-shade-default {
  background-color: var(--rz-base);
  color: var(--rz-on-base);
}
.rz-button.rz-base.rz-shade-dark {
  background-color: var(--rz-base-dark);
  color: var(--rz-on-base-dark);
}
.rz-button.rz-base.rz-shade-darker {
  background-color: var(--rz-base-darker);
  color: var(--rz-on-base-darker);
}

.rz-button.rz-light {
  background-color: var(--rz-base-lighter);
  color: var(--rz-on-base-lighter);
}

.rz-button.rz-dark {
  background-color: var(--rz-base-darker);
  color: var(--rz-on-base-darker);
}

/*# sourceMappingURL=software-dark.css.map */
