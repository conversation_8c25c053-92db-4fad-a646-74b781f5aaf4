$overlay-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.06) !default;
$overlay-border: var(--rz-border-normal)  !default;
$overlay-background-color: var(--rz-base-100) !default;

.rz-overlaypanel {
  position: absolute;
  overflow: hidden;

  box-shadow: var(--rz-overlay-shadow);
  border: var(--rz-overlay-border);
  border-radius: var(--rz-border-radius);
  background-color: var(--rz-overlay-background-color);
}
