$button-base-background-color: var(--rz-primary) !default;
$button-base-color: var(--rz-on-primary) !default;
$button-background-size: auto !default;
$button-border-radius: var(--rz-border-radius) !default;
$button-shadow: none !default;
$button-transition: var(--rz-transition-all), width 0, height 0 !default;
$button-line-height: 1.25rem !default;
$button-vertical-align: top !default;
$button-hover-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2) !default;
$button-hover-gradient: linear-gradient(rgba(#fff, 0.1), rgba(#fff, 0.1)) !default;
$button-hover-background-size: $button-background-size !default;
$button-focus-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2) !default;
$button-focus-gradient: linear-gradient(rgba(#fff, 0.1), rgba(#fff, 0.1)) !default;
$button-focus-background-size: $button-background-size !default;
$button-focus-outline: var(--rz-outline-focus) !default;
$button-focus-outline-offset: var(--rz-outline-offset) !default;
$button-active-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1) !default;
$button-active-gradient: linear-gradient(rgba(#000, 0.1), rgba(#000, 0.1)) !default;
$button-active-background-size: $button-background-size !default;
$button-disabled-opacity: 0.2 !default;
$button-empty-opacity: 0.4 !default;

$button-sizes: () !default;

$button-sizes: map-merge(
  (
    lg: (
      font-size: 1rem,
      padding: 0.875rem 1.5rem,
      line-height: 1.5rem,
      height: auto,
      min-height: 3.25rem,
      min-width: 3.25rem,
      font-weight: 600,
      border-radius: calc(2 * var(--rz-border-radius)),
      gap: 0.25rem,
      icon: (
        font-size: 1.5rem,
        height: 1.5rem,
        line-height: 1.5rem,
        width: 1.5rem
      ),
      text-with-icon-padding: 0 1.375rem,
      icon-only-padding: 0.875rem
    ),
    md: (
      font-size: var(--rz-body-font-size),
      padding: 0.5rem 1rem,
      line-height: 1.25rem,
      height: auto,
      min-height: 2.25rem,
      min-width: 2.25rem,
      font-weight: 600,
      gap: 0.25rem,
      icon: (
        font-size: var(--rz-icon-size),
        line-height: var(--rz-icon-size),
        width: var(--rz-icon-size),
        height: var(--rz-icon-size)
      ),
      text-with-icon-padding: 0.5rem 1rem 0.5rem 0.5rem,
      icon-only-padding: 0.5rem
    ),
    sm: (
      font-size: var(--rz-body-font-size),
      padding: 0.25rem 0.75rem,
      line-height: 1.25rem,
      height: auto,
      min-height: 1.75rem,
      min-width: 1.75rem,
      gap: 0.25rem,
      icon: (
        font-size: var(--rz-icon-size),
        line-height: var(--rz-icon-size),
        width: var(--rz-icon-size),
        height: var(--rz-icon-size)
      ),
      text-with-icon-padding: 0.25rem 0.75rem 0.25rem 0.25rem,
      icon-only-padding: 0.25rem
    ),
    xs: (
      font-size: 0.75rem,
      padding: 0.125rem 0.25rem,
      line-height: 1rem,
      height: auto,
      min-height: 1.25rem,
      min-width: 1.25rem,
      font-weight: 600,
      gap: 0.125rem,
      icon: (
        font-size: calc(0.8 * var(--rz-icon-size)),
        line-height: calc(0.8 * var(--rz-icon-size)),
        width: calc(0.8 * var(--rz-icon-size)),
        height: calc(0.8 * var(--rz-icon-size))
      ),
      text-with-icon-padding: 0.125rem 0.5rem 0.125rem 0.125rem,
      icon-only-padding: 0.125rem
    )
  ),
  $button-sizes
);

%button, .rz-button {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  color: var(--rz-button-base-color);
  background-color: var(--rz-button-base-background-color);
  background-size: var(--rz-button-background-size);
  background-repeat: no-repeat;
  border: none;
  border-radius: var(--rz-button-border-radius);
  outline: none;
  box-shadow: var(--rz-button-shadow);
  font-size: 1.0625rem;
  font-family: inherit;
  line-height: var(--rz-button-line-height);
  text-decoration: none;
  transition: var(--rz-button-transition);
  -webkit-appearance: none;

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-button-focus-outline);
    outline-offset: var(--rz-button-focus-outline-offset);
  }

  &:not(.rz-state-disabled) {
    cursor: pointer;

    &:not(:active) {
      @include rz-hover-state {
        text-decoration: none;
        background-image: var(--rz-button-hover-gradient);
        background-size: var(--rz-button-hover-background-size);
        box-shadow: var(--rz-button-hover-shadow);
      }
      
      &:focus-visible {
        text-decoration: none;
        background-image: var(--rz-button-focus-gradient);
        background-size: var(--rz-button-focus-background-size);
        box-shadow: var(--rz-button-focus-shadow);
      }
    }

    &:active {
      text-decoration: none;
      background-image: var(--rz-button-active-gradient);
      background-size: var(--rz-button-active-background-size);
      box-shadow: var(--rz-button-active-shadow);
    }
  }

  &.rz-state-disabled {
    opacity: var(--rz-button-disabled-opacity);
    cursor: initial;
  }

  &.rz-state-empty {
    opacity: var(--rz-button-empty-opacity);
    cursor: initial;
  }

  .rz-button-box {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    vertical-align: var(--rz-button-vertical-align);
    line-height: var(--rz-button-line-height);
  }

  .rz-button-text {
    vertical-align: var(--rz-button-vertical-align);
  }

  .rzi {
    vertical-align: var(--rz-button-vertical-align);
  }
}

// Focus Styles
@each $style, $button in map-merge($severity-styles-map, $base-styles-map) {
  .rz-button.rz-#{$style}:focus-visible {
    @if $style != 'dark' and $style != 'light' {
      outline-color: var(--rz-#{$style});
    } @else {
      outline-color: var(--rz-outline-color);
    }
  }
}

@each $style, $button in map-merge($severity-styles-map, $base-styles-map) {
  .rz-button.rz-#{$style} {
    @each $name, $value in $button {
      @if $style == 'dark' {
        #{$name}: #{$value};
      } 
      @else if $style == 'light' {
        #{$name}: #{$value};
      } 
      @else {
        #{$name}: #{$value};
        @if $name == 'background-color' {
          &.rz-shade-lighter { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); }
          &.rz-shade-light { background-color: var(--rz-#{$style}-light); color: var(--rz-on-#{$style}-light); }
          &.rz-shade-default { background-color: var(--rz-#{$style}); color: var(--rz-on-#{$style}); }
          &.rz-shade-dark { background-color: var(--rz-#{$style}-dark); color: var(--rz-on-#{$style}-dark); }
          &.rz-shade-darker { background-color: var(--rz-#{$style}-darker); color: var(--rz-on-#{$style}-darker); }
        }
      }
    }
    &.rz-variant-flat {
      box-shadow: none !important;

      &:focus-visible {
        @if $style != 'dark' and $style != 'light' {
          outline-color: var(--rz-#{$style});
        } 
        @else {
          outline-color: var(--rz-outline-color);
        }
      }
    }
  }
}

@each $style, $button in map-merge($severity-styles-map, $base-styles-map) {
  .rz-button.rz-variant-outlined.rz-#{$style} {
    @each $name, $value in $button {
      @if $name == 'background-color' {
        @if $style == 'dark' {
          &.rz-shade-default { box-shadow: inset 0 0 0 var(--rz-border-width) #{$value}; color: #{$value}; }
        } 
        @else if $style == 'light' {
          @if $theme-dark == true {
            &.rz-shade-default { box-shadow: inset 0 0 0 var(--rz-border-width) map-get($button, color); color: map-get($button, color); }
          } 
          @else {
            &.rz-shade-default { box-shadow: inset 0 0 0 var(--rz-border-width) #{$value}; color: #{$value}; }
          }
        } 
        @else if $style == 'base' {
          &.rz-shade-lighter { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-lighter); color: var(--rz-#{$style}-light); }
          &.rz-shade-light { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-light); color: var(--rz-#{$style}-light); }
          &.rz-shade-default { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-text-color); color: var(--rz-text-color); }
          &.rz-shade-dark { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-dark); color: var(--rz-#{$style}-dark); }
          &.rz-shade-darker { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-darker); color: var(--rz-#{$style}-darker); }
        } 
        @else {
          &.rz-shade-lighter { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-lighter); color: var(--rz-#{$style}-light); }
          &.rz-shade-light { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-light); color: var(--rz-#{$style}-light); }
          &.rz-shade-default { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}); color: var(--rz-#{$style}); }
          &.rz-shade-dark { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-dark); color: var(--rz-#{$style}-dark); }
          &.rz-shade-darker { box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-darker); color: var(--rz-#{$style}-darker); }
        }
        background-color: transparent;

        &:not(.rz-state-disabled) {
          @include rz-hover-state {
            @if $style == 'light' { background-color: rgba(255, 255, 255, .12); } 
            @else if $style == 'dark' { background-color: rgba(0, 0, 0, .12); }
            @else if $style == 'base' {
              @if $theme-dark == true { background-color: var(--rz-#{$style}-darker); color: var(--rz-on-#{$style}-darker); } 
              @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); }
            }
            @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); } 
            background-image: none;
          }
        
          &:active {
            @if $style == 'light' { background-color: rgba(255, 255, 255, .12); } 
            @else if $style == 'dark' { background-color: rgba(0, 0, 0, .12); }
            @else if $style == 'base' {
              @if $theme-dark == true { background-color: var(--rz-#{$style}-darker); color: var(--rz-on-#{$style}-darker); } 
              @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); }
            }
            @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); } 
            background-image: none;
          }
        }
      }
    }
  }
}

@each $style, $button in map-merge($severity-styles-map, $base-styles-map) {
  .rz-button.rz-variant-text.rz-#{$style} {
    @each $name, $value in $button {
      @if $name == 'background-color' {
        @if $style == 'dark' { color: #{$value}; }
        @else if $style == 'light' {
          @if $theme-dark == true { color: map-get($button, color); } 
          @else { color: #{$value}; }
        } @else if $style == 'base' {
          &.rz-shade-lighter { color: var(--rz-#{$style}-lighter); }
          &.rz-shade-light { color: var(--rz-#{$style}-light); }
          &.rz-shade-default { color: inherit }
          &.rz-shade-dark { color: var(--rz-#{$style}-dark); }
          &.rz-shade-darker { color: var(--rz-#{$style}-darker); }
        } @else {
          &.rz-shade-lighter { color: var(--rz-#{$style}-lighter); }
          &.rz-shade-light { color: var(--rz-#{$style}-light); }
          &.rz-shade-default { color: var(--rz-#{$style}); }
          &.rz-shade-dark { color: var(--rz-#{$style}-dark); }
          &.rz-shade-darker { color: var(--rz-#{$style}-darker); }
        }
        background-color: transparent;
        box-shadow: none;

        &:not(.rz-state-disabled) {
          @include rz-hover-state {
            @if $style == 'light' { background-color: rgba(255, 255, 255, .12); } 
            @else if $style == 'dark' { background-color: rgba(0, 0, 0, .12); } 
            @else if $style == 'base' {
              @if $theme-dark == true { background-color: var(--rz-#{$style}-darker); color: var(--rz-on-#{$style}-darker); }
              @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); }
            }
            @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); } 
            background-image: none;
            box-shadow: none;
          }

          &:active {
            @if $style == 'light' { background-color: rgba(255, 255, 255, .12); } 
            @else if $style == 'dark' { background-color: rgba(0, 0, 0, .12); } 
            @else if $style == 'base' {
              @if $theme-dark == true { background-color: var(--rz-#{$style}-darker); color: var(--rz-on-#{$style}-darker); }
              @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); }
            }
            @else { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); } 
            background-image: none;
            box-shadow: none;
          }
        }
      }

    }
  }
}

@each $size, $button in $button-sizes {
  %button-#{$size}, .rz-button-#{$size} {
    padding: map-get($button, padding);
    height: map-get($button, height);
    line-height: map-get($button, line-height);
    min-height: map-get($button, min-height);
    min-width: map-get($button, min-width);
    border-radius: map-get($button, border-radius);
    font-size: map-get($button, font-size);
    font-weight: map-get($button, font-weight);
    letter-spacing: map-get($button, letter-spacing);
    text-transform: map-get($button, text-transform);

    & .rz-button-box { line-height: map-get($button, line-height); gap: map-get($button, gap);}
    &.rz-button-icon-left { padding: map-get($button, text-with-icon-padding); }
    &.rz-button-icon-only { padding: map-get($button, icon-only-padding); }

    .rzi {
      @each $name, $value in map-get($button, icon) {
        #{$name}: #{$value};
      }
    }
  }
}

@keyframes button-icon-spin {
  from { transform:rotate(0deg); }
  to { transform:rotate(360deg); }
}
