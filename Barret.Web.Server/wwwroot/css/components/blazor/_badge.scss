﻿$badge-border-radius: var(--rz-border-radius) !default;
$badge-pill-border-radius: calc(4 * var(--rz-border-radius)) !default;
$badge-padding: 0.125rem 0.25rem !default;
$badge-pill-padding: 0.125rem 0.5rem !default;
$badge-font-size: 0.75rem !default;
$badge-font-weight: 600 !default;
$badge-line-height: 1rem !default;
$badge-text-transform: uppercase !default;
$badge-letter-spacing: 0.02rem !default;

.rz-badge {
    box-sizing: border-box;
    color: var(--rz-text-contrast-color);
    display: inline-block;
    padding: var(--rz-badge-padding);
    font-size: var(--rz-badge-font-size);
    font-weight: var(--rz-badge-font-weight);
    line-height: var(--rz-badge-line-height);
    text-align: center;
    text-transform: var(--rz-badge-text-transform);
    white-space: nowrap;
    border-radius: var(--rz-badge-border-radius);
    letter-spacing: var(--rz-badge-letter-spacing);

    .rz-button & {
      vertical-align: top;
    }
}

@each $style, $badge in map-merge($severity-styles-map, $base-styles-map) {
  .rz-badge-#{$style} {
    @each $name, $value in $badge {
      @if $style == 'dark' {
        #{$name}: #{$value};
      } @else if $style == 'light' {
        #{$name}: #{$value};
      } @else {
        #{$name}: #{$value};
        @if $name == 'background-color' {
          &.rz-shade-lighter {
            background-color: var(--rz-#{$style}-lighter);
            color: var(--rz-on-#{$style}-lighter);
          }
          &.rz-shade-light {
            background-color: var(--rz-#{$style}-light);
            color: var(--rz-on-#{$style}-light);
          }
          &.rz-shade-default {
            background-color: var(--rz-#{$style});
            color: var(--rz-on-#{$style});
          }
          &.rz-shade-dark {
            background-color: var(--rz-#{$style}-dark);
            color: var(--rz-on-#{$style}-dark);
          }
          &.rz-shade-darker {
            background-color: var(--rz-#{$style}-darker);
            color: var(--rz-on-#{$style}-darker);
          }
        }
      }
    }
  }
}

@each $style, $badge in map-merge($severity-styles-map, $base-styles-map) {
  .rz-variant-outlined.rz-badge-#{$style} {
    @each $name, $value in $badge {
      @if $name == 'background-color' {
        @if $style == 'dark' {
          &.rz-shade-default {
            box-shadow: inset 0 0 0 var(--rz-border-width) #{$value};
            color: #{$value};
          }
        } @else if $style == 'light' {
          &.rz-shade-default {
            box-shadow: inset 0 0 0 var(--rz-border-width) #{$value};
            color: #{$value};
          }
        } @else {
          &.rz-shade-lighter {
            box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-lighter);
            color: var(--rz-#{$style}-lighter);
          }
          &.rz-shade-light {
            box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-light);
            color: var(--rz-#{$style}-light);
          }
          &.rz-shade-default {
            box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style});
            color: var(--rz-#{$style});
          }
          &.rz-shade-dark {
            box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-dark);
            color: var(--rz-#{$style}-dark);
          }
          &.rz-shade-darker {
            box-shadow: inset 0 0 0 var(--rz-border-width) var(--rz-#{$style}-darker);
            color: var(--rz-#{$style}-darker);
          }
        }
        background-color: transparent;
      }
    }
  }
}

@each $style, $badge in map-merge($severity-styles-map, $base-styles-map) {
  .rz-variant-text.rz-badge-#{$style} {
    @each $name, $value in $badge {
      @if $name == 'background-color' {
        @if $style == 'dark' {
          color: #{$value};
        } @else if $style == 'light' {
          color: #{$value};
        } @else {
          &.rz-shade-lighter {
            color: var(--rz-#{$style}-lighter)
          }
          &.rz-shade-light {
            color: var(--rz-#{$style}-light)
          }
          &.rz-shade-default {
            color: var(--rz-#{$style})
          }
          &.rz-shade-dark {
            color: var(--rz-#{$style}-dark)
          }
          &.rz-shade-darker {
            color: var(--rz-#{$style}-darker)
          }
        }
        background-color: transparent;
      }
    }
  }
}

.rz-badge-pill {
    border-radius: var(--rz-badge-pill-border-radius);
    padding: var(--rz-badge-pill-padding);
}
