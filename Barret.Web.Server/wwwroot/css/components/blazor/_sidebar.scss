$sidebar-z: 1 !default;
$sidebar-width: 250px !default;
$sidebar-border-inline-end: var(--rz-border-normal) !default;
$sidebar-background-color: var(--rz-base-800) !default;
$sidebar-color: var(--rz-white) !default;

.rz-sidebar {
  background-color: var(--rz-sidebar-background-color);
  color: var(--rz-sidebar-color);
  border-inline-end: var(--rz-sidebar-border-inline-end);
  position: fixed;
  overflow: auto;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: var(--rz-sidebar-z);
  width: var(--rz-sidebar-width);
  opacity: 1;
  transition: width var(--rz-transition), opacity var(--rz-transition);
}

@media (max-width: 768px) { 
  .rz-sidebar-responsive {
    width: 0;
    opacity: 0;
  }
}

.rz-sidebar-expanded {
  width: var(--rz-sidebar-width);
  opacity: 1;
}

.rz-sidebar-collapsed {
  width: 0 !important;
  opacity: 0;
}
