$slider-background-color: var(--rz-base-background-color) !default;
$slider-border: var(--rz-border-normal) !default;
$slider-border-radius: var(--rz-border-radius) !default;
$slider-horizontal-height: 0.5rem !default;
$slider-horizontal-width: 10rem !default;
$slider-range-background-color: var(--rz-secondary-lighter) !default;
$slider-range-border: var(--rz-border-secondary-light) !default;
$slider-handle-width: 0.75rem !default;
$slider-handle-height: 1.5rem !default;
$slider-handle-color: var(--rz-on-secondary) !default;
$slider-handle-background-color: var(--rz-secondary) !default;
$slider-handle-border: none !default;
$slider-handle-border-radius: calc(var(--rz-border-radius) / 2) !default;
$slider-handle-shadow: none !default;
$slider-handle-transition: background-color var(--rz-transition), color var(--rz-transition), border var(--rz-transition), box-shadow var(--rz-transition) !default;
$slider-handle-hover-border: $slider-handle-border !default;
$slider-handle-hover-background-color: var(--rz-secondary-light) !default;
$slider-handle-hover-shadow: inset 0 -3px 0 0 rgba(#fff, 0.2) !default;
$slider-handle-focus-outline: var(--rz-outline-focus) !default;
$slider-handle-focus-outline-offset: var(--rz-outline-offset) !default;
$slider-disabled-background-color: var(--rz-base-background-color) !default;
$slider-disabled-border: var(--rz-border-disabled) !default;
$slider-disabled-range-background-color: var(--rz-base-100) !default;
$slider-disabled-range-border: var(--rz-border-normal) !default;
$slider-disabled-handle-background-color: var(--rz-base-300) !default;
$slider-disabled-handle-border: none !default;

.rz-slider {
  box-sizing: border-box;
  position: relative;
  display: inline-block;
  border: var(--rz-slider-border);
  border-radius: var(--rz-slider-border-radius);
  background-color: var(--rz-slider-background-color);

  &.rz-state-disabled {
    background-color: var(--rz-slider-disabled-background-color);
    border: var(--rz-slider-disabled-border);

    .rz-slider-range {
      background-color: var(--rz-slider-disabled-range-background-color);
      border: var(--rz-slider-disabled-range-border);
    }

    .rz-slider-handle {
      background-color: var(--rz-slider-disabled-handle-background-color);
      border: var(--rz-slider-disabled-handle-border);
    }
  }
}

.rz-slider-range {
  position: absolute;
  background-color: var(--rz-slider-range-background-color);
  border: var(--rz-slider-range-border);
}

.rz-slider-handle {
  position: absolute;
  background-color: var(--rz-slider-handle-background-color);
  border: var(--rz-slider-handle-border);
  border-radius: var(--rz-slider-handle-border-radius);
  box-shadow: var(--rz-slider-handle-shadow);
  transition: var(--rz-slider-handle-transition);
}

.rz-slider-horizontal {
  height: var(--rz-slider-horizontal-height);
  width: var(--rz-slider-horizontal-width);

  .rz-slider-range {
    inset-block-start: -1px;
    inset-block-end: -1px;
    inset-inline-start: -1px;
    border-start-start-radius: var(--rz-slider-border-radius);
    border-end-start-radius: var(--rz-slider-border-radius);
  }

  .rz-slider-handle {
    top: 50%;
    transform: translateY(-50%);
    margin-inline-start: calc(var(--rz-slider-handle-width) / -2);
    width: var(--rz-slider-handle-width);
    height: var(--rz-slider-handle-height);
  }
}

.rz-slider-vertical {
  width: var(--rz-slider-horizontal-height);
  height: var(--rz-slider-horizontal-width);

  .rz-slider-range {
    inset-inline-start: -1px;
    inset-inline-end: -1px;
    inset-block-end: -1px;
    border-end-start-radius: var(--rz-slider-border-radius);
    border-end-end-radius: var(--rz-slider-border-radius);
  }

    .rz-slider-handle {
      left: 50%;
      transform: translateX(-50%);
      margin-block-start: calc(var(--rz-slider-handle-width) / -2);
      width: var(--rz-slider-handle-height);
      height: var(--rz-slider-handle-width);
  }
}

.rz-slider:not(.rz-state-disabled) {
  .rz-slider-handle {
    &:hover,
    &:focus,
    &:active {
      background-color: var(--rz-slider-handle-hover-background-color);
      box-shadow: var(--rz-slider-handle-hover-shadow);
      border: var(--rz-slider-handle-hover-border);
      cursor: pointer;
      outline: none;
    }

    &:focus {
      outline: var(--rz-outline-normal);
    }

    &:focus-visible {
      outline: var(--rz-slider-handle-focus-outline);
      outline-offset: var(--rz-slider-handle-focus-outline-offset);
    }
  }
}
