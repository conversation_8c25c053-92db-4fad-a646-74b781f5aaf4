$tooltip-background-color: var(--rz-info) !default;
$tooltip-color: var(--rz-on-info) !default;
$tooltip-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.15) !default;
$tooltip-padding: 0.25rem 0.5rem !default;
$tooltip-border-radius: var(--rz-border-radius) !default;
$tooltip-font-size: var(--rz-body-font-size) !default;

.rz-tooltip {
  box-sizing: border-box;
  position: absolute;
  transition: top 0.2s, left 0.2s;
  top: 0;
  left: 0;
}

.rz-tooltip-content {
  background: var(--rz-tooltip-background-color);
  color: var(--rz-tooltip-color);
  box-shadow: var(--rz-tooltip-shadow);
  padding: var(--rz-tooltip-padding);
  border-radius: var(--rz-tooltip-border-radius);
  font-size: var(--rz-tooltip-font-size);
  white-space: nowrap;
}

.rz-tooltip .rz-top-tooltip-content {
  margin-bottom: 16px;
}
.rz-tooltip .rz-top-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  inset-block-end: 0;
  inset-inline-start: 12px;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -12px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit;
}

.rz-tooltip .rz-bottom-tooltip-content {
  margin-top: -8px;
}
.rz-tooltip .rz-bottom-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  inset-block-start: 0;
  inset-inline-start: 12px;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -12px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit;
}

.rz-tooltip .rz-left-tooltip-content {
  margin-right: 8px;
}
.rz-tooltip .rz-left-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  top: 20px;
  right: 0;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -10px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit;
}

.rz-tooltip .rz-right-tooltip-content {
  margin-left: 0;
}
.rz-tooltip .rz-right-tooltip-content:after {
  content: ' ';
  position: absolute;
  width: 8px;
  height: 8px;
  top: 20px;
  left: 0;
  background-color: inherit;
  transform-origin: center;
  transform: translate(-50%, -10px) rotate(45deg);
  border-bottom: inherit;
  border-right: inherit;
}
