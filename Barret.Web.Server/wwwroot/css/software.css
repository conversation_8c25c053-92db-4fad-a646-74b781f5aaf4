@import url("https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap");
@font-face {
  font-family: "Material Symbols Outlined";
  font-style: normal;
  font-weight: 100 700;
  font-display: block;
  src: url("../fonts/MaterialSymbolsOutlined.woff2") format("woff2");
}
@font-face {
  font-family: "Roboto Flex";
  font-style: normal;
  font-weight: 100 1000;
  font-display: swap;
  src: url("../fonts/RobotoFlex.woff2") format("woff2");
}
@font-face {
  font-family: "Source Sans 3";
  font-style: normal;
  font-weight: 200 900;
  font-display: swap;
  src: url("../fonts/SourceSans3VF-Upright.ttf.woff2") format("woff2");
}
@font-face {
  font-family: "Source Sans 3";
  font-style: italic;
  font-weight: 200 900;
  font-display: swap;
  src: url("../fonts/SourceSans3VF-Italic.ttf.woff2") format("woff2");
}
:root {
  --rz-font-family: Source Sans Pro, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  --rz-font-size: 0.875rem;
  --rz-line-height: 1.429;
  --rz-icon-font-family: "Material Symbols Outlined";
}

body {
  font-family: var(--rz-font-family);
  font-size: var(--rz-font-size);
  line-height: var(--rz-line-height);
}

.rzi {
  font-family: var(--rz-icon-font-family);
  font-weight: normal;
  font-style: normal;
  font-size: var(--rz-icon-size);
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

:root,
.rz-software {
  --rz-white: #ffffff;
}

:root,
.rz-software {
  --rz-black: #000000;
}

:root,
.rz-software {
  --rz-base: #ffffff;
}

:root,
.rz-software {
  --rz-base-50: #ffffff;
}

:root,
.rz-software {
  --rz-base-100: #f9fafb;
}

:root,
.rz-software {
  --rz-base-200: #f3f4f6;
}

:root,
.rz-software {
  --rz-base-300: #e5e7eb;
}

:root,
.rz-software {
  --rz-base-400: #d1d5db;
}

:root,
.rz-software {
  --rz-base-500: #9ca3af;
}

:root,
.rz-software {
  --rz-base-600: #6b7280;
}

:root,
.rz-software {
  --rz-base-700: #374151;
}

:root,
.rz-software {
  --rz-base-800: #1f2937;
}

:root,
.rz-software {
  --rz-base-900: #111827;
}

:root,
.rz-software {
  --rz-base-light: #f3f4f6;
}

:root,
.rz-software {
  --rz-base-lighter: #ffffff;
}

:root,
.rz-software {
  --rz-base-dark: #374151;
}

:root,
.rz-software {
  --rz-base-darker: #111827;
}

:root,
.rz-software {
  --rz-primary: #000000;
}

:root,
.rz-software {
  --rz-primary-light: rgb(38.25, 38.25, 38.25);
}

:root,
.rz-software {
  --rz-primary-lighter: rgba(0, 0, 0, 0.08);
}

:root,
.rz-software {
  --rz-primary-dark: black;
}

:root,
.rz-software {
  --rz-primary-darker: black;
}

:root,
.rz-software {
  --rz-secondary: #ffffff;
}

:root,
.rz-software {
  --rz-secondary-light: white;
}

:root,
.rz-software {
  --rz-secondary-lighter: rgba(255, 255, 255, 0.08);
}

:root,
.rz-software {
  --rz-secondary-dark: rgb(216.75, 216.75, 216.75);
}

:root,
.rz-software {
  --rz-secondary-darker: rgb(178.5, 178.5, 178.5);
}

:root,
.rz-software {
  --rz-info: #737373;
}

:root,
.rz-software {
  --rz-info-light: #888888;
}

:root,
.rz-software {
  --rz-info-lighter: rgba(115, 115, 115, 0.08);
}

:root,
.rz-software {
  --rz-info-dark: rgb(97.75, 97.75, 97.75);
}

:root,
.rz-software {
  --rz-info-darker: rgb(80.5, 80.5, 80.5);
}

:root,
.rz-software {
  --rz-success: #000000;
}

:root,
.rz-software {
  --rz-success-light: rgb(38.25, 38.25, 38.25);
}

:root,
.rz-software {
  --rz-success-lighter: rgba(0, 0, 0, 0.08);
}

:root,
.rz-software {
  --rz-success-dark: black;
}

:root,
.rz-software {
  --rz-success-darker: black;
}

:root,
.rz-software {
  --rz-warning: #404040;
}

:root,
.rz-software {
  --rz-warning-light: rgb(92.65, 92.65, 92.65);
}

:root,
.rz-software {
  --rz-warning-lighter: rgba(64, 64, 64, 0.08);
}

:root,
.rz-software {
  --rz-warning-dark: rgb(54.4, 54.4, 54.4);
}

:root,
.rz-software {
  --rz-warning-darker: rgb(44.8, 44.8, 44.8);
}

:root,
.rz-software {
  --rz-danger: #000000;
}

:root,
.rz-software {
  --rz-danger-light: rgb(38.25, 38.25, 38.25);
}

:root,
.rz-software {
  --rz-danger-lighter: rgba(0, 0, 0, 0.08);
}

:root,
.rz-software {
  --rz-danger-dark: black;
}

:root,
.rz-software {
  --rz-danger-darker: black;
}

:root,
.rz-software {
  --rz-on-base: #000000;
}

:root,
.rz-software {
  --rz-on-base-light: #000000;
}

:root,
.rz-software {
  --rz-on-base-lighter: #000000;
}

:root,
.rz-software {
  --rz-on-base-dark: #ffffff;
}

:root,
.rz-software {
  --rz-on-base-darker: #ffffff;
}

:root,
.rz-software {
  --rz-on-primary: #ffffff;
}

:root,
.rz-software {
  --rz-on-primary-light: #ffffff;
}

:root,
.rz-software {
  --rz-on-primary-lighter: #000000;
}

:root,
.rz-software {
  --rz-on-primary-dark: #ffffff;
}

:root,
.rz-software {
  --rz-on-primary-darker: #ffffff;
}

:root,
.rz-software {
  --rz-on-secondary: #ffffff;
}

:root,
.rz-software {
  --rz-on-secondary-light: #ffffff;
}

:root,
.rz-software {
  --rz-on-secondary-lighter: #ffffff;
}

:root,
.rz-software {
  --rz-on-secondary-dark: #ffffff;
}

:root,
.rz-software {
  --rz-on-secondary-darker: #ffffff;
}

:root,
.rz-software {
  --rz-on-info: #ffffff;
}

:root,
.rz-software {
  --rz-on-info-light: #ffffff;
}

:root,
.rz-software {
  --rz-on-info-lighter: #737373;
}

:root,
.rz-software {
  --rz-on-info-dark: #ffffff;
}

:root,
.rz-software {
  --rz-on-info-darker: #ffffff;
}

:root,
.rz-software {
  --rz-on-success: #ffffff;
}

:root,
.rz-software {
  --rz-on-success-light: #ffffff;
}

:root,
.rz-software {
  --rz-on-success-lighter: #000000;
}

:root,
.rz-software {
  --rz-on-success-dark: #ffffff;
}

:root,
.rz-software {
  --rz-on-success-darker: #ffffff;
}

:root,
.rz-software {
  --rz-on-warning: #000000;
}

:root,
.rz-software {
  --rz-on-warning-light: #000000;
}

:root,
.rz-software {
  --rz-on-warning-lighter: #404040;
}

:root,
.rz-software {
  --rz-on-warning-dark: #000000;
}

:root,
.rz-software {
  --rz-on-warning-darker: #000000;
}

:root,
.rz-software {
  --rz-on-danger: #ffffff;
}

:root,
.rz-software {
  --rz-on-danger-light: #ffffff;
}

:root,
.rz-software {
  --rz-on-danger-lighter: #000000;
}

:root,
.rz-software {
  --rz-on-danger-dark: #ffffff;
}

:root,
.rz-software {
  --rz-on-danger-darker: #ffffff;
}

:root,
.rz-software {
  --rz-border-width: 1px;
  --rz-border-radius: 4px;
  --rz-body-font-size: 0.875rem;
  --rz-body-line-height: 1.429;
  --rz-icon-size: 1rem;
  --rz-outline-offset: 2px;
  --rz-outline-width: 2px;
  --rz-outline-color: var(--rz-primary);
  --rz-outline-normal: none;
  --rz-outline-focus: 2px solid var(--rz-outline-color);
  --rz-transition-all: all 0.2s ease-in-out;
  --rz-button-base-background-color: var(--rz-primary);
  --rz-button-base-color: var(--rz-on-primary);
  --rz-button-border-radius: 4px;
  --rz-button-background-size: auto;
  --rz-button-shadow: none;
  --rz-button-transition: var(--rz-transition-all), width 0, height 0;
  --rz-button-line-height: 1.25rem;
  --rz-button-vertical-align: top;
  --rz-button-hover-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2);
  --rz-button-hover-gradient: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  --rz-button-hover-background-size: auto;
  --rz-button-focus-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2);
  --rz-button-focus-gradient: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  --rz-button-focus-background-size: auto;
  --rz-button-focus-outline: var(--rz-outline-focus);
  --rz-button-focus-outline-offset: var(--rz-outline-offset);
  --rz-button-active-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1);
  --rz-button-active-gradient: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
  --rz-button-active-background-size: auto;
  --rz-button-disabled-opacity: 0.2;
  --rz-button-empty-opacity: 0.4;
  --rz-grid-simple-filter-icon-active-color: var(--rz-secondary-darker);
  --rz-grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter);
}

body {
  background-color: var(--rz-body-background-color, var(--rz-base-100));
  color: var(--rz-text-color, var(--rz-on-base));
  font-family: var(--rz-font-family);
  font-size: var(--rz-body-font-size);
  line-height: var(--rz-body-line-height);
}

.rz-button {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  color: var(--rz-button-base-color);
  background-color: var(--rz-button-base-background-color);
  background-size: var(--rz-button-background-size);
  background-repeat: no-repeat;
  border: none;
  border-radius: var(--rz-button-border-radius);
  outline: none;
  box-shadow: var(--rz-button-shadow);
  font-size: 1.0625rem;
  font-family: inherit;
  line-height: var(--rz-button-line-height);
  text-decoration: none;
  transition: var(--rz-button-transition);
  -webkit-appearance: none;
  cursor: pointer;
  padding: 0.5rem 1rem;
}
.rz-button:focus {
  outline: var(--rz-outline-normal);
}
.rz-button:focus-visible {
  outline: var(--rz-button-focus-outline);
  outline-offset: var(--rz-button-focus-outline-offset);
}
.rz-button:not(.rz-state-disabled):not(:active):hover {
  text-decoration: none;
  background-image: var(--rz-button-hover-gradient);
  background-size: var(--rz-button-hover-background-size);
  box-shadow: var(--rz-button-hover-shadow);
}
.rz-button:not(.rz-state-disabled):not(:active):focus-visible {
  text-decoration: none;
  background-image: var(--rz-button-focus-gradient);
  background-size: var(--rz-button-focus-background-size);
  box-shadow: var(--rz-button-focus-shadow);
}
.rz-button:not(.rz-state-disabled):active {
  text-decoration: none;
  background-image: var(--rz-button-active-gradient);
  background-size: var(--rz-button-active-background-size);
  box-shadow: var(--rz-button-active-shadow);
}
.rz-button.rz-state-disabled {
  opacity: var(--rz-button-disabled-opacity);
  cursor: initial;
}
.rz-button .rz-button-box {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  vertical-align: var(--rz-button-vertical-align);
  line-height: var(--rz-button-line-height);
}
.rz-button .rz-button-text {
  vertical-align: var(--rz-button-vertical-align);
}
.rz-button .rzi {
  vertical-align: var(--rz-button-vertical-align);
}

.rz-button.rz-primary {
  background-color: var(--rz-primary);
  color: var(--rz-on-primary);
}
.rz-button.rz-primary.rz-shade-lighter {
  background-color: var(--rz-primary-lighter);
  color: var(--rz-on-primary-lighter);
}
.rz-button.rz-primary.rz-shade-light {
  background-color: var(--rz-primary-light);
  color: var(--rz-on-primary-light);
}
.rz-button.rz-primary.rz-shade-default {
  background-color: var(--rz-primary);
  color: var(--rz-on-primary);
}
.rz-button.rz-primary.rz-shade-dark {
  background-color: var(--rz-primary-dark);
  color: var(--rz-on-primary-dark);
}
.rz-button.rz-primary.rz-shade-darker {
  background-color: var(--rz-primary-darker);
  color: var(--rz-on-primary-darker);
}

.rz-button.rz-secondary {
  background-color: var(--rz-secondary);
  color: var(--rz-on-secondary);
}
.rz-button.rz-secondary.rz-shade-lighter {
  background-color: var(--rz-secondary-lighter);
  color: var(--rz-on-secondary-lighter);
}
.rz-button.rz-secondary.rz-shade-light {
  background-color: var(--rz-secondary-light);
  color: var(--rz-on-secondary-light);
}
.rz-button.rz-secondary.rz-shade-default {
  background-color: var(--rz-secondary);
  color: var(--rz-on-secondary);
}
.rz-button.rz-secondary.rz-shade-dark {
  background-color: var(--rz-secondary-dark);
  color: var(--rz-on-secondary-dark);
}
.rz-button.rz-secondary.rz-shade-darker {
  background-color: var(--rz-secondary-darker);
  color: var(--rz-on-secondary-darker);
}

.rz-button.rz-info {
  background-color: var(--rz-info);
  color: var(--rz-on-info);
}
.rz-button.rz-info.rz-shade-lighter {
  background-color: var(--rz-info-lighter);
  color: var(--rz-on-info-lighter);
}
.rz-button.rz-info.rz-shade-light {
  background-color: var(--rz-info-light);
  color: var(--rz-on-info-light);
}
.rz-button.rz-info.rz-shade-default {
  background-color: var(--rz-info);
  color: var(--rz-on-info);
}
.rz-button.rz-info.rz-shade-dark {
  background-color: var(--rz-info-dark);
  color: var(--rz-on-info-dark);
}
.rz-button.rz-info.rz-shade-darker {
  background-color: var(--rz-info-darker);
  color: var(--rz-on-info-darker);
}

.rz-button.rz-success {
  background-color: var(--rz-success);
  color: var(--rz-on-success);
}
.rz-button.rz-success.rz-shade-lighter {
  background-color: var(--rz-success-lighter);
  color: var(--rz-on-success-lighter);
}
.rz-button.rz-success.rz-shade-light {
  background-color: var(--rz-success-light);
  color: var(--rz-on-success-light);
}
.rz-button.rz-success.rz-shade-default {
  background-color: var(--rz-success);
  color: var(--rz-on-success);
}
.rz-button.rz-success.rz-shade-dark {
  background-color: var(--rz-success-dark);
  color: var(--rz-on-success-dark);
}
.rz-button.rz-success.rz-shade-darker {
  background-color: var(--rz-success-darker);
  color: var(--rz-on-success-darker);
}

.rz-button.rz-warning {
  background-color: var(--rz-warning);
  color: var(--rz-on-warning);
}
.rz-button.rz-warning.rz-shade-lighter {
  background-color: var(--rz-warning-lighter);
  color: var(--rz-on-warning-lighter);
}
.rz-button.rz-warning.rz-shade-light {
  background-color: var(--rz-warning-light);
  color: var(--rz-on-warning-light);
}
.rz-button.rz-warning.rz-shade-default {
  background-color: var(--rz-warning);
  color: var(--rz-on-warning);
}
.rz-button.rz-warning.rz-shade-dark {
  background-color: var(--rz-warning-dark);
  color: var(--rz-on-warning-dark);
}
.rz-button.rz-warning.rz-shade-darker {
  background-color: var(--rz-warning-darker);
  color: var(--rz-on-warning-darker);
}

.rz-button.rz-danger {
  background-color: var(--rz-danger);
  color: var(--rz-on-danger);
}
.rz-button.rz-danger.rz-shade-lighter {
  background-color: var(--rz-danger-lighter);
  color: var(--rz-on-danger-lighter);
}
.rz-button.rz-danger.rz-shade-light {
  background-color: var(--rz-danger-light);
  color: var(--rz-on-danger-light);
}
.rz-button.rz-danger.rz-shade-default {
  background-color: var(--rz-danger);
  color: var(--rz-on-danger);
}
.rz-button.rz-danger.rz-shade-dark {
  background-color: var(--rz-danger-dark);
  color: var(--rz-on-danger-dark);
}
.rz-button.rz-danger.rz-shade-darker {
  background-color: var(--rz-danger-darker);
  color: var(--rz-on-danger-darker);
}

.rz-button.rz-base {
  background-color: var(--rz-base);
  color: var(--rz-on-base);
}
.rz-button.rz-base.rz-shade-lighter {
  background-color: var(--rz-base-lighter);
  color: var(--rz-on-base-lighter);
}
.rz-button.rz-base.rz-shade-light {
  background-color: var(--rz-base-light);
  color: var(--rz-on-base-light);
}
.rz-button.rz-base.rz-shade-default {
  background-color: var(--rz-base);
  color: var(--rz-on-base);
}
.rz-button.rz-base.rz-shade-dark {
  background-color: var(--rz-base-dark);
  color: var(--rz-on-base-dark);
}
.rz-button.rz-base.rz-shade-darker {
  background-color: var(--rz-base-darker);
  color: var(--rz-on-base-darker);
}

.rz-button.rz-light {
  background-color: var(--rz-base-lighter);
  color: var(--rz-on-base-lighter);
}

.rz-button.rz-dark {
  background-color: var(--rz-base-darker);
  color: var(--rz-on-base-darker);
}

.rz-button {
  border-radius: 9999px !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  letter-spacing: 0.01em !important;
  transition: all 0.2s ease-out !important;
  border: none !important;
  box-shadow: none !important;
  text-transform: none !important;
  min-height: 40px !important;
  padding: 0.75rem 1.5rem !important;
}
.rz-button:hover:not(.rz-state-disabled) {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}
.rz-button:active:not(.rz-state-disabled) {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}
.rz-button.rz-state-disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.rz-dialog {
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  border: 1px solid var(--rz-base-300) !important;
  background: white !important;
  max-width: 95vw !important;
  max-height: 95vh !important;
  overflow: hidden !important;
}
.rz-dialog .rz-dialog-content {
  padding: 0 !important;
  border-radius: inherit !important;
}
.rz-dialog .rz-dialog-titlebar {
  display: none !important;
}

.rz-dialog-mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(2px) !important;
}

.rz-textbox, .rz-textarea, .rz-numeric input, .rz-dropdown {
  border: 1px solid var(--rz-base-300) !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.875rem !important;
  background-color: white !important;
  color: var(--rz-base-900) !important;
  transition: all 0.2s ease-out !important;
  min-height: 40px !important;
}
.rz-textbox:focus, .rz-textarea:focus, .rz-numeric input:focus, .rz-dropdown:focus {
  border-color: var(--rz-primary) !important;
  box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
}
.rz-textbox:hover:not(:focus), .rz-textarea:hover:not(:focus), .rz-numeric input:hover:not(:focus), .rz-dropdown:hover:not(:focus) {
  border-color: var(--rz-base-400) !important;
}

.rz-datatable {
  border: 1px solid var(--rz-base-200) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background-color: white !important;
}
.rz-datatable .rz-datatable-header {
  background-color: var(--rz-base-50) !important;
  border-bottom: 1px solid var(--rz-base-200) !important;
}
.rz-datatable .rz-datatable-header th {
  padding: 1rem !important;
  font-weight: 500 !important;
  color: var(--rz-base-700) !important;
  font-size: 0.875rem !important;
  border-right: 1px solid var(--rz-base-200) !important;
}
.rz-datatable .rz-datatable-data td {
  padding: 1rem !important;
  border-bottom: 1px solid var(--rz-base-100) !important;
  border-right: 1px solid var(--rz-base-100) !important;
  font-size: 0.875rem !important;
  color: var(--rz-base-900) !important;
}
.rz-datatable .rz-datatable-data tr:hover {
  background-color: var(--rz-base-50) !important;
}
.rz-datatable .rz-datatable-data tr:last-child td {
  border-bottom: none !important;
}

.rz-card {
  background-color: white !important;
  border-radius: 12px !important;
  border: 1px solid var(--rz-base-200) !important;
  box-shadow: none !important;
  transition: all 0.2s ease-out !important;
  overflow: hidden !important;
}
.rz-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  border-color: var(--rz-base-300) !important;
}
.rz-card .rz-card-header {
  padding: 1.5rem 1.5rem 1rem !important;
  border-bottom: 1px solid var(--rz-base-200) !important;
  background-color: transparent !important;
}
.rz-card .rz-card-content {
  padding: 1.5rem !important;
}
.rz-card .rz-card-footer {
  padding: 1rem 1.5rem 1.5rem !important;
  border-top: 1px solid var(--rz-base-200) !important;
  background-color: var(--rz-base-50) !important;
}

.vehicle-card-with-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: box-shadow 0.5s ease !important;
  height: 100% !important;
  cursor: pointer !important;
}
.vehicle-card-with-shadow:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 6px 8px -2px rgba(0, 0, 0, 0.04) !important;
}

.vehicle-type-card-with-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: box-shadow 0.5s ease !important;
  height: 100% !important;
  cursor: pointer !important;
}
.vehicle-type-card-with-shadow:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 6px 8px -2px rgba(0, 0, 0, 0.04) !important;
}
.vehicle-type-card-with-shadow:hover .rounded-full {
  background-color: var(--rz-base-800) !important;
}

h1:focus {
  outline: none !important;
}

#blazor-error-ui {
  background: lightyellow;
  bottom: 0;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  padding: 0.6rem 1.25rem 0.7rem 1.25rem;
  position: fixed;
  width: 100%;
  z-index: 1000;
}
#blazor-error-ui .reload {
  color: var(--rz-primary);
  cursor: pointer;
  text-decoration: none;
}
#blazor-error-ui .reload:hover {
  text-decoration: underline;
}
#blazor-error-ui .dismiss {
  cursor: pointer;
  position: absolute;
  right: 0.75rem;
  top: 0.5rem;
}

/*# sourceMappingURL=software.css.map */
