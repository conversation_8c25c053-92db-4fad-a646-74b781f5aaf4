/*
 * Theme-Aware Color Variables
 * Supports light/dark mode switching via Radzen ThemeService
 * Layout, spacing, and typography should use Tailwind classes directly in Razor files
 */

:root {
  /* Light Theme (Default) - Monochromatic Black & White */
  --primary: #000000;
  --primary-hover: #262626;
  --primary-foreground: #ffffff;

  --secondary: #ffffff;
  --secondary-hover: #f5f5f5;
  --secondary-foreground: #000000;

  --muted: #f5f5f5;
  --muted-foreground: #737373;

  --border: #e5e5e5;
  --border-hover: #d4d4d4;

  --background: #ffffff;
  --foreground: #000000;

  /* Status colors - monochromatic */
  --success: #000000;
  --warning: #404040;
  --error: #000000;
  --info: #737373;
}

/* Dark Theme - Inverted Monochromatic */
[data-theme="dark"] {
  --primary: #ffffff;
  --primary-hover: #e5e5e5;
  --primary-foreground: #000000;

  --secondary: #171717;
  --secondary-hover: #262626;
  --secondary-foreground: #ffffff;

  --muted: #262626;
  --muted-foreground: #a3a3a3;

  --border: #404040;
  --border-hover: #525252;

  --background: #000000;
  --foreground: #ffffff;

  /* Status colors - adapted for dark theme */
  --success: #ffffff;
  --warning: #d4d4d4;
  --error: #ffffff;
  --info: #a3a3a3;
}
  --shadow-md: 0 1px 1px rgba(0, 0, 0, 0.01), 0 2px 3px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 1px 1px rgba(0, 0, 0, 0.01), 0 2px 4px rgba(0, 0, 0, 0.02), 0 3px 6px rgba(0, 0, 0, 0.03);

  /* Hover shadow - minimal at top, slightly more on sides, most at bottom */
  --shadow-hover: 0 1px 2px rgba(0, 0, 0, 0.01), 0 2px 4px rgba(0, 0, 0, 0.02), 0 4px 8px rgba(0, 0, 0, 0.03);

  /* Spacing Scale (based on 4px base unit) */
  --space-2xs: 0.25rem;  /* 4px */
  --space-xs: 0.5rem;    /* 8px */
  --space-sm: 0.75rem;   /* 12px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */

  /* Legacy spacing variables (for backward compatibility) */
  --space-1: var(--space-2xs);
  --space-2: var(--space-xs);
  --space-3: var(--space-md);
  --space-4: var(--space-lg);
  --space-5: var(--space-xl);
  --space-6: var(--space-2xl);

  /* Typography */
  --font-family-base: 'Inter', system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

  /* Font Sizes */
  --font-size-xs: 0.75rem;      /* 12px - Caption */
  --font-size-sm: 0.875rem;     /* 14px - Small body, button */
  --font-size-base: 1rem;       /* 16px - Body, input */
  --font-size-lg: 1.125rem;     /* 18px - H4 */
  --font-size-xl: 1.25rem;      /* 20px - H3 */
  --font-size-2xl: 1.5rem;      /* 24px - H2 */
  --font-size-3xl: 2rem;        /* 32px - H1 */

  /* Font Weights */
  --font-weight-normal: 400;    /* Regular */
  --font-weight-medium: 500;    /* Medium */
  --font-weight-semibold: 600;  /* Semi-bold */
  --font-weight-bold: 700;      /* Bold */

  /* Line Heights */
  --line-height-none: 1;        /* For buttons */
  --line-height-tight: 1.2;     /* For headings */
  --line-height-snug: 1.3;      /* For subheadings */
  --line-height-normal: 1.5;    /* For body text */
  --line-height-relaxed: 1.75;  /* For readable text */

  /* Letter Spacing */
  --letter-spacing-tight: -0.02em;  /* For large headings */
  --letter-spacing-normal: 0;       /* For most text */
  --letter-spacing-wide: 0.01em;    /* For buttons, labels */
  --letter-spacing-wider: 0.02em;   /* For small text */

  /* Transitions */
  --transition-fast: 0.2s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;

  /* Z-index layers */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Container sizes */
  --container-max-width: 1440px; /* 90rem */
  --container-lg-width: 1200px;
  --container-md-width: 992px;
  --container-sm-width: 768px;
  --container-xs-width: 576px;

  /* Component-specific variables */
  /* Buttons */
  --button-sm-height: 32px;
  --button-md-height: 40px;
  --button-lg-height: 48px;
  --button-sm-padding: var(--space-xs) var(--space-md); /* 8px 16px */
  --button-md-padding: var(--space-sm) var(--space-lg); /* 12px 24px */
  --button-lg-padding: var(--space-md) var(--space-xl); /* 16px 32px */
  --button-border-radius: var(--border-radius-pill); /* Fully rounded */
  --button-focus-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);

  /* Inputs */
  --input-height: 40px;
  --input-padding: 0 var(--space-md); /* 0 16px */
  --input-border-radius: var(--border-radius-sm); /* 8px */
  --input-focus-shadow: 0 0 0 3px rgba(95, 90, 137, 0.2);

  /* Cards */
  --card-padding: var(--space-lg); /* 24px */
  --card-header-padding: var(--space-lg) var(--space-lg) var(--space-md); /* 24px 24px 16px */
  --card-footer-padding: var(--space-md) var(--space-lg) var(--space-lg); /* 16px 24px 24px */
  --card-border-radius: var(--border-radius-md); /* 8px */
  --card-compact-padding: var(--space-md); /* 16px */
  --card-compact-border-radius: var(--border-radius-sm); /* 6px */

  /* Navigation */
  --nav-item-gap: var(--space-xl); /* 32px */
  --nav-vertical-width: 240px;
  --nav-vertical-item-gap: var(--space-xs); /* 8px */
  --nav-vertical-item-padding: var(--space-sm) var(--space-md); /* 12px 16px */

  /* Bootstrap theme overrides */
  --bs-primary: var(--accent-500);
  --bs-primary-rgb: 95, 90, 137; /* #5f5a89 */
  --bs-secondary: var(--gray-100);
  --bs-secondary-rgb: 245, 245, 245; /* #f5f5f5 */
  --bs-success: var(--success);
  --bs-success-rgb: 74, 222, 128; /* #4ade80 */
  --bs-info: var(--info);
  --bs-info-rgb: 96, 165, 250; /* #60a5fa */
  --bs-warning: var(--warning);
  --bs-warning-rgb: 251, 191, 36; /* #fbbf24 */
  --bs-danger: var(--error);
  --bs-danger-rgb: 248, 113, 113; /* #f87171 */
  --bs-light: var(--gray-100);
  --bs-light-rgb: 245, 245, 245; /* #f5f5f5 */
  --bs-dark: var(--gray-900);
  --bs-dark-rgb: 23, 23, 23; /* #171717 */
  --bs-font-sans-serif: var(--font-family-base);
  --bs-body-font-family: var(--font-family-base);
  --bs-body-font-size: var(--font-size-base);
  --bs-body-font-weight: var(--font-weight-normal);
  --bs-body-line-height: var(--line-height-normal);
  --bs-body-color: var(--gray-900);
  --bs-body-bg: var(--gray-50);
  --bs-border-color: var(--gray-300);
  --bs-border-radius: var(--border-radius-sm);
  --bs-border-radius-lg: var(--border-radius-md);
  --bs-border-radius-sm: 0.25rem;


}
