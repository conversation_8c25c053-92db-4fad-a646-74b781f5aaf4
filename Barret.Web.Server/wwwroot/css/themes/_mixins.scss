// Radzen Theme Mixins
// This file contains essential mixins for Radzen theme compilation

// Hover state mixin
@mixin rz-hover-state {
  &:hover {
    @content;
  }
}

// Focus state mixin
@mixin rz-focus-state {
  &:focus,
  &:focus-visible {
    @content;
  }
}

// Active state mixin
@mixin rz-active-state {
  &:active {
    @content;
  }
}

// Disabled state mixin
@mixin rz-disabled-state {
  &.rz-state-disabled,
  &:disabled {
    @content;
  }
}

// Button variant mixin
@mixin button-variant($bg-color, $text-color, $hover-bg: null, $active-bg: null) {
  background-color: $bg-color;
  color: $text-color;
  
  @if $hover-bg {
    @include rz-hover-state {
      background-color: $hover-bg;
    }
  }
  
  @if $active-bg {
    @include rz-active-state {
      background-color: $active-bg;
    }
  }
}

// Border radius mixin
@mixin border-radius($radius: $rz-border-radius) {
  border-radius: $radius;
}

// Transition mixin
@mixin transition($properties: all, $duration: $rz-transition-duration, $timing: $rz-transition-timing-function) {
  transition: $properties $duration $timing;
}

// Box shadow mixin
@mixin box-shadow($shadow) {
  box-shadow: $shadow;
}

// Text truncate mixin
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Flexbox center mixin
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// Screen reader only mixin
@mixin sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
