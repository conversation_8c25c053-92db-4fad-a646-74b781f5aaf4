// Radzen Theme Variables
// This file contains the core variables and mixins needed for Radzen theme compilation
// Based on the official Radzen Blazor repository structure

// Theme Configuration
$theme-name: 'software' !default;
$theme-dark: false !default;
$base: false !default;

// Core Radzen Variables
$rz-border-width: 1px !default;
$rz-border-radius: 4px !default;
$rz-root-font-size: 16px !default;
$rz-body-font-size: 0.875rem !default;
$rz-body-line-height: 1.429 !default;
$rz-text-font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !default;
$rz-outline-offset: 2px !default;
$rz-outline-width: 2px !default;
$rz-icon-size: 1rem !default;

// Transition Variables
$rz-transition-duration: 0.2s !default;
$rz-transition-timing-function: ease-in-out !default;
$rz-transition-all: all $rz-transition-duration $rz-transition-timing-function !default;

// Component-specific variables
$button-border-radius: $rz-border-radius !default;
$input-border-radius: $rz-border-radius !default;
$card-border-radius: calc(2 * #{$rz-border-radius}) !default;

// Grid variables
$grid-simple-filter-icon-active-color: var(--rz-secondary-darker) !default;
$grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter) !default;

// Severity Styles Map (for buttons, alerts, etc.)
$severity-styles-map: () !default;
$severity-styles-map: map-merge(
  (
    primary: (
      background-color: var(--rz-primary),
      color: var(--rz-on-primary)
    ),
    secondary: (
      background-color: var(--rz-secondary),
      color: var(--rz-on-secondary)
    ),
    info: (
      background-color: var(--rz-info),
      color: var(--rz-on-info)
    ),
    success: (
      background-color: var(--rz-success),
      color: var(--rz-on-success)
    ),
    warning: (
      background-color: var(--rz-warning),
      color: var(--rz-on-warning)
    ),
    danger: (
      background-color: var(--rz-danger),
      color: var(--rz-on-danger)
    )
  ),
  $severity-styles-map
);

// Base Styles Map
$base-styles-map: () !default;
$base-styles-map: map-merge(
  (
    base: (
      background-color: var(--rz-base),
      color: var(--rz-on-base)
    ),
    light: (
      background-color: var(--rz-base-lighter),
      color: var(--rz-on-base-lighter)
    ),
    dark: (
      background-color: var(--rz-base-darker),
      color: var(--rz-on-base-darker)
    )
  ),
  $base-styles-map
);
