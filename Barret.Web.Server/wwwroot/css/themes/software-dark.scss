@import 'variables';
@import 'mixins';
$theme-dark: true;
$theme-name: software-dark;

// Dark Theme Colors

$rz-white: #ffffff !default;
$rz-black: #000000 !default;

$rz-base: #505f65 !default;
$rz-primary: #598087 !default;
$rz-secondary: #80a4ab !default;
$rz-info: #2cc8c8 !default;
$rz-success: #5dbf74 !default;
$rz-warning: #fac152 !default;
$rz-danger: #f9777f !default;

$rz-base-50: #ffffff !default;
$rz-base-100: #f5f8f9 !default;
$rz-base-200: #e9eef0 !default;
$rz-base-300: #dae0e2 !default;
$rz-base-400: #c1c8cb !default;
$rz-base-500: #95a2a8 !default;
$rz-base-600: #77858b !default;
$rz-base-700: #505f65 !default;
$rz-base-800: #3a474d !default;
$rz-base-900: #28363c !default;
$rz-base-light: #c1c9cb !default;
$rz-base-lighter: #ffffff !default;
$rz-base-dark: #3a474d !default;
$rz-base-darker: #28363c !default;

$rz-primary-light: mix($rz-white, $rz-primary, 12%)!default;
$rz-primary-lighter: rgba($rz-primary, .16) !default;
$rz-primary-dark: mix($rz-black, $rz-primary, 8%) !default;
$rz-primary-darker: mix($rz-black, $rz-primary, 25%) !default;

$rz-secondary-light: mix($rz-white, $rz-secondary, 12%) !default;
$rz-secondary-lighter: rgba($rz-secondary, .20) !default;
$rz-secondary-dark: mix($rz-black, $rz-secondary, 8%) !default;
$rz-secondary-darker: mix($rz-black, $rz-secondary, 25%) !default;

$rz-info-light: mix($rz-white, $rz-info, 16%) !default;
$rz-info-lighter: rgba($rz-info, .20) !default;
$rz-info-dark: mix($rz-black, $rz-info, 16%) !default;
$rz-info-darker: mix($rz-black, $rz-info, 25%) !default;

$rz-success-light: mix($rz-white, $rz-success, 16%) !default;
$rz-success-lighter: rgba($rz-success, .16) !default;
$rz-success-dark: mix($rz-black, $rz-success, 16%) !default;
$rz-success-darker: mix($rz-black, $rz-success, 25%) !default;

$rz-warning-light: mix($rz-white, $rz-warning, 16%) !default;
$rz-warning-lighter: rgba($rz-warning, .20) !default;
$rz-warning-dark: mix($rz-black, $rz-warning, 16%) !default;
$rz-warning-darker: mix($rz-black, $rz-warning, 25%) !default;

$rz-danger-light: mix($rz-white, $rz-danger, 16%) !default;
$rz-danger-lighter: rgba($rz-danger, .20) !default;
$rz-danger-dark: mix($rz-black, $rz-danger, 16%) !default;
$rz-danger-darker: mix($rz-black, $rz-danger, 25%) !default;

// Dark Theme Constants
$rz-body-background-color: var(--rz-base-900) !default;
$rz-text-color: var(--rz-base-50) !default;
$rz-text-contrast-color: var(--rz-base-50) !default;

// Grid
$grid-simple-filter-icon-active-color: var(--rz-secondary-darker) !default;
$grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter) !default;

// Borders
$rz-border-normal: var(--rz-border-width) solid var(--rz-base-700) !default;
$rz-border-hover: var(--rz-border-width) solid var(--rz-base-600) !default;
$rz-border-focus: var(--rz-border-width) solid var(--rz-base-600) !default;
$rz-border-disabled: var(--rz-border-width) solid var(--rz-base-700) !default;

// Theme Colors Map
$rz-theme-colors-map: () !default;
$rz-theme-colors-map: map-merge(
  (
    "white": $rz-white,
    "black": $rz-black,
    
    "base": $rz-base,
    "base-50": $rz-base-50,
    "base-100": $rz-base-100,
    "base-200": $rz-base-200,
    "base-300": $rz-base-300,
    "base-400": $rz-base-400,
    "base-500": $rz-base-500,
    "base-600": $rz-base-600,
    "base-700": $rz-base-700,
    "base-800": $rz-base-800,
    "base-900": $rz-base-900,
    "base-light": $rz-base-light,
    "base-lighter": $rz-base-lighter,
    "base-dark": $rz-base-dark,
    "base-darker": $rz-base-darker,
    
    "primary": $rz-primary,
    "primary-light": $rz-primary-light,
    "primary-lighter": $rz-primary-lighter,
    "primary-dark": $rz-primary-dark,
    "primary-darker": $rz-primary-darker,
    
    "secondary": $rz-secondary,
    "secondary-light": $rz-secondary-light,
    "secondary-lighter": $rz-secondary-lighter,
    "secondary-dark": $rz-secondary-dark,
    "secondary-darker": $rz-secondary-darker,
    
    "info": $rz-info,
    "info-light": $rz-info-light,
    "info-lighter": $rz-info-lighter,
    "info-dark": $rz-info-dark,
    "info-darker": $rz-info-darker,
    
    "success": $rz-success,
    "success-light": $rz-success-light,
    "success-lighter": $rz-success-lighter,
    "success-dark": $rz-success-dark,
    "success-darker": $rz-success-darker,
    
    "warning": $rz-warning,
    "warning-light": $rz-warning-light,
    "warning-lighter": $rz-warning-lighter,
    "warning-dark": $rz-warning-dark,
    "warning-darker": $rz-warning-darker,
    
    "danger": $rz-danger,
    "danger-light": $rz-danger-light,
    "danger-lighter": $rz-danger-lighter,
    "danger-dark": $rz-danger-dark,
    "danger-darker": $rz-danger-darker,
    
    "on-base": $rz-white,
    "on-base-light": $rz-white,
    "on-base-lighter": $rz-black,
    "on-base-dark": $rz-white,
    "on-base-darker": $rz-white,
    "on-primary": $rz-white,
    "on-primary-light": $rz-white,
    "on-primary-lighter": $rz-primary,
    "on-primary-dark": $rz-white,
    "on-primary-darker": $rz-white,
    "on-secondary": $rz-white,
    "on-secondary-light": $rz-white,
    "on-secondary-lighter": $rz-secondary,
    "on-secondary-dark": $rz-white,
    "on-secondary-darker": $rz-white,
    "on-info": $rz-white,
    "on-info-light": $rz-white,
    "on-info-lighter": $rz-info,
    "on-info-dark": $rz-white,
    "on-info-darker": $rz-white,
    "on-success": $rz-white,
    "on-success-light": $rz-white,
    "on-success-lighter": $rz-success,
    "on-success-dark": $rz-white,
    "on-success-darker": $rz-white,
    "on-warning": $rz-black,
    "on-warning-light": $rz-black,
    "on-warning-lighter": $rz-warning,
    "on-warning-dark": $rz-black,
    "on-warning-darker": $rz-black,
    "on-danger": $rz-white,
    "on-danger-light": $rz-white,
    "on-danger-lighter": $rz-danger,
    "on-danger-dark": $rz-white,
    "on-danger-darker": $rz-white,
    
    "text-color": $rz-text-color,
    "text-contrast-color": $rz-text-contrast-color
  ),
  $rz-theme-colors-map
);

@import 'fonts';
@import 'components';
