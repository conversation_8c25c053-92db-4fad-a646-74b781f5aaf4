@import 'variables';
@import 'mixins';
$theme-name: software;

// Theme Colors - Black/White Minimalistic Design System

$rz-white: #ffffff !default;
$rz-black: #000000 !default;

// Primary colors - Black/White system
$rz-base: #ffffff !default;
$rz-primary: #000000 !default;
$rz-secondary: #ffffff !default;
$rz-info: #737373 !default;
$rz-success: #000000 !default;
$rz-warning: #404040 !default;
$rz-danger: #000000 !default;

$rz-series-1: #376df5 !default;
$rz-series-2: #64dfdf !default;
$rz-series-3: #f68769 !default;
$rz-series-4: #c161e2 !default;
$rz-series-5: #fdd07a !default;
$rz-series-6: #f8629b !default;
$rz-series-7: #74d062 !default;
$rz-series-8: #84a7ff !default;
$rz-series-9: #4d99f9 !default;
$rz-series-10: #8cecec !default;
$rz-series-11: #fab793 !default;
$rz-series-12: #da88ee !default;
$rz-series-13: #fee3ab !default;
$rz-series-14: #fb89c3 !default;
$rz-series-15: #a2e389 !default;
$rz-series-16: #b5caff !default;
$rz-series-17: #1750f3 !default;
$rz-series-18: #46d7d7 !default;
$rz-series-19: #f46e4c !default;
$rz-series-20: #b343db !default;
$rz-series-21: #fdc55f !default;
$rz-series-22: #f64485 !default;
$rz-series-23: #58c544 !default;
$rz-series-24: #6a93ff !default;

// Grayscale palette for black/white design system
$rz-base-50: #ffffff !default;
$rz-base-100: #f9fafb !default;
$rz-base-200: #f3f4f6 !default;
$rz-base-300: #e5e7eb !default;
$rz-base-400: #d1d5db !default;
$rz-base-500: #9ca3af !default;
$rz-base-600: #6b7280 !default;
$rz-base-700: #374151 !default;
$rz-base-800: #1f2937 !default;
$rz-base-900: #111827 !default;
$rz-base-light: #f3f4f6 !default;
$rz-base-lighter: #ffffff !default;
$rz-base-dark: #374151 !default;
$rz-base-darker: #111827 !default;

// Unified color variations - automatically derived from base colors
// This ensures changing the base color automatically updates all variations
$rz-primary-light: mix($rz-white, $rz-primary, 15%) !default;
$rz-primary-lighter: rgba($rz-primary, .08) !default;
$rz-primary-dark: mix($rz-black, $rz-primary, 15%) !default;
$rz-primary-darker: mix($rz-black, $rz-primary, 30%) !default;

$rz-secondary-light: mix($rz-white, $rz-secondary, 15%) !default;
$rz-secondary-lighter: rgba($rz-secondary, .08) !default;
$rz-secondary-dark: mix($rz-black, $rz-secondary, 15%) !default;
$rz-secondary-darker: mix($rz-black, $rz-secondary, 30%) !default;

$rz-info-light: mix($rz-white, $rz-info, 15%) !default;
$rz-info-lighter: rgba($rz-info, .08) !default;
$rz-info-dark: mix($rz-black, $rz-info, 15%) !default;
$rz-info-darker: mix($rz-black, $rz-info, 30%) !default;

$rz-success-light: mix($rz-white, $rz-success, 15%) !default;
$rz-success-lighter: rgba($rz-success, .08) !default;
$rz-success-dark: mix($rz-black, $rz-success, 15%) !default;
$rz-success-darker: mix($rz-black, $rz-success, 30%) !default;

$rz-warning-light: mix($rz-white, $rz-warning, 15%) !default;
$rz-warning-lighter: rgba($rz-warning, .08) !default;
$rz-warning-dark: mix($rz-black, $rz-warning, 15%) !default;
$rz-warning-darker: mix($rz-black, $rz-warning, 30%) !default;

$rz-danger-light: mix($rz-white, $rz-danger, 15%) !default;
$rz-danger-lighter: rgba($rz-danger, .08) !default;
$rz-danger-dark: mix($rz-black, $rz-danger, 15%) !default;
$rz-danger-darker: mix($rz-black, $rz-danger, 30%) !default;

// Theme Constants

$rz-border-width: 1px !default;
$rz-border-radius: 4px !default;
$rz-root-font-size: 16px !default;
$rz-body-font-size: 0.875rem !default;
$rz-body-line-height: 1.429 !default;
$rz-body-background-color: var(--rz-base-100) !default;
$rz-text-font-family: 'Source Sans Pro', -apple-system, BlinkMacSystemFont,
'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji',
'Segoe UI Emoji', 'Segoe UI Symbol' !default;
$rz-outline-offset: 2px !default;
$rz-outline-width: 2px !default;
$rz-outline-color: var(--rz-primary) !default;

// Grid
$grid-simple-filter-icon-active-color: var(--rz-secondary-darker) !default;
$grid-simple-filter-icon-active-background-color: var(--rz-secondary-lighter) !default;

// Theme Colors Map
$rz-theme-colors-map: () !default;
$rz-theme-colors-map: map-merge(
  (
    "white": $rz-white,
    "black": $rz-black,

    "base": $rz-base,
    "base-50": $rz-base-50,
    "base-100": $rz-base-100,
    "base-200": $rz-base-200,
    "base-300": $rz-base-300,
    "base-400": $rz-base-400,
    "base-500": $rz-base-500,
    "base-600": $rz-base-600,
    "base-700": $rz-base-700,
    "base-800": $rz-base-800,
    "base-900": $rz-base-900,
    "base-light": $rz-base-light,
    "base-lighter": $rz-base-lighter,
    "base-dark": $rz-base-dark,
    "base-darker": $rz-base-darker,

    "primary": $rz-primary,
    "primary-light": $rz-primary-light,
    "primary-lighter": $rz-primary-lighter,
    "primary-dark": $rz-primary-dark,
    "primary-darker": $rz-primary-darker,

    "secondary": $rz-secondary,
    "secondary-light": $rz-secondary-light,
    "secondary-lighter": $rz-secondary-lighter,
    "secondary-dark": $rz-secondary-dark,
    "secondary-darker": $rz-secondary-darker,

    "info": $rz-info,
    "info-light": $rz-info-light,
    "info-lighter": $rz-info-lighter,
    "info-dark": $rz-info-dark,
    "info-darker": $rz-info-darker,

    "success": $rz-success,
    "success-light": $rz-success-light,
    "success-lighter": $rz-success-lighter,
    "success-dark": $rz-success-dark,
    "success-darker": $rz-success-darker,

    "warning": $rz-warning,
    "warning-light": $rz-warning-light,
    "warning-lighter": $rz-warning-lighter,
    "warning-dark": $rz-warning-dark,
    "warning-darker": $rz-warning-darker,

    "danger": $rz-danger,
    "danger-light": $rz-danger-light,
    "danger-lighter": $rz-danger-lighter,
    "danger-dark": $rz-danger-dark,
    "danger-darker": $rz-danger-darker,

    "on-base": $rz-black,
    "on-base-light": $rz-black,
    "on-base-lighter": $rz-black,
    "on-base-dark": $rz-white,
    "on-base-darker": $rz-white,
    "on-primary": $rz-white,
    "on-primary-light": $rz-white,
    "on-primary-lighter": $rz-primary,
    "on-primary-dark": $rz-white,
    "on-primary-darker": $rz-white,
    "on-secondary": $rz-white,
    "on-secondary-light": $rz-white,
    "on-secondary-lighter": $rz-secondary,
    "on-secondary-dark": $rz-white,
    "on-secondary-darker": $rz-white,
    "on-info": $rz-white,
    "on-info-light": $rz-white,
    "on-info-lighter": $rz-info,
    "on-info-dark": $rz-white,
    "on-info-darker": $rz-white,
    "on-success": $rz-white,
    "on-success-light": $rz-white,
    "on-success-lighter": $rz-success,
    "on-success-dark": $rz-white,
    "on-success-darker": $rz-white,
    "on-warning": $rz-black,
    "on-warning-light": $rz-black,
    "on-warning-lighter": $rz-warning,
    "on-warning-dark": $rz-black,
    "on-warning-darker": $rz-black,
    "on-danger": $rz-white,
    "on-danger-light": $rz-white,
    "on-danger-lighter": $rz-danger,
    "on-danger-dark": $rz-white,
    "on-danger-darker": $rz-white
  ),
  $rz-theme-colors-map
);

@import 'fonts';
@import 'components';

// ===== CUSTOM RADZEN COMPONENT OVERRIDES =====
// These overrides ensure Radzen components match our design system

// Button Styling - Clean, rounded design
.rz-button {
  border-radius: 9999px !important; // Fully rounded
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  letter-spacing: 0.01em !important;
  transition: all 0.2s ease-out !important;
  border: none !important;
  box-shadow: none !important;
  text-transform: none !important;
  min-height: 40px !important;
  padding: 0.75rem 1.5rem !important;

  &:hover:not(.rz-state-disabled) {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }

  &:active:not(.rz-state-disabled) {
    transform: translateY(0) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  &.rz-state-disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
  }
}

// Dialog Styling - Clean, modern appearance
.rz-dialog {
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  border: 1px solid var(--rz-base-300) !important;
  background: white !important;
  max-width: 95vw !important;
  max-height: 95vh !important;
  overflow: hidden !important;

  .rz-dialog-content {
    padding: 0 !important;
    border-radius: inherit !important;
  }

  .rz-dialog-titlebar {
    display: none !important; // Hide default titlebar for custom headers
  }
}

// Dialog overlay
.rz-dialog-mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(2px) !important;
}

// Form Input Styling
.rz-textbox, .rz-textarea, .rz-numeric input, .rz-dropdown {
  border: 1px solid var(--rz-base-300) !important;
  border-radius: 8px !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.875rem !important;
  background-color: white !important;
  color: var(--rz-base-900) !important;
  transition: all 0.2s ease-out !important;
  min-height: 40px !important;

  &:focus {
    border-color: var(--rz-primary) !important;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1) !important;
    outline: none !important;
  }

  &:hover:not(:focus) {
    border-color: var(--rz-base-400) !important;
  }
}

// Data Grid Styling
.rz-datatable {
  border: 1px solid var(--rz-base-200) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background-color: white !important;

  .rz-datatable-header {
    background-color: var(--rz-base-50) !important;
    border-bottom: 1px solid var(--rz-base-200) !important;

    th {
      padding: 1rem !important;
      font-weight: 500 !important;
      color: var(--rz-base-700) !important;
      font-size: 0.875rem !important;
      border-right: 1px solid var(--rz-base-200) !important;
    }
  }

  .rz-datatable-data {
    td {
      padding: 1rem !important;
      border-bottom: 1px solid var(--rz-base-100) !important;
      border-right: 1px solid var(--rz-base-100) !important;
      font-size: 0.875rem !important;
      color: var(--rz-base-900) !important;
    }

    tr:hover {
      background-color: var(--rz-base-50) !important;
    }

    tr:last-child td {
      border-bottom: none !important;
    }
  }
}

// Card Styling
.rz-card {
  background-color: white !important;
  border-radius: 12px !important;
  border: 1px solid var(--rz-base-200) !important;
  box-shadow: none !important;
  transition: all 0.2s ease-out !important;
  overflow: hidden !important;

  &:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    border-color: var(--rz-base-300) !important;
  }

  .rz-card-header {
    padding: 1.5rem 1.5rem 1rem !important;
    border-bottom: 1px solid var(--rz-base-200) !important;
    background-color: transparent !important;
  }

  .rz-card-content {
    padding: 1.5rem !important;
  }

  .rz-card-footer {
    padding: 1rem 1.5rem 1.5rem !important;
    border-top: 1px solid var(--rz-base-200) !important;
    background-color: var(--rz-base-50) !important;
  }
}

// ===== APPLICATION-SPECIFIC STYLING =====
// Vehicle and component-specific styles consolidated from separate CSS files

// Vehicle card shadow effects
.vehicle-card-with-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: box-shadow 0.5s ease !important;
  height: 100% !important;
  cursor: pointer !important;

  &:hover {
    box-shadow:
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 4px 6px -1px rgba(0, 0, 0, 0.05),
      0 6px 8px -2px rgba(0, 0, 0, 0.04) !important;
  }
}

// Vehicle type card shadow effects
.vehicle-type-card-with-shadow {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  transition: box-shadow 0.5s ease !important;
  height: 100% !important;
  cursor: pointer !important;

  &:hover {
    box-shadow:
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 4px 6px -1px rgba(0, 0, 0, 0.05),
      0 6px 8px -2px rgba(0, 0, 0, 0.04) !important;

    .rounded-full {
      background-color: var(--rz-base-800) !important;
    }
  }
}

// Focus styles
h1:focus {
  outline: none !important;
}

// Blazor error UI
#blazor-error-ui {
  background: lightyellow;
  bottom: 0;
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
  display: none;
  left: 0;
  padding: 0.6rem 1.25rem 0.7rem 1.25rem;
  position: fixed;
  width: 100%;
  z-index: 1000;

  .reload {
    color: var(--rz-primary);
    cursor: pointer;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
  }
}
