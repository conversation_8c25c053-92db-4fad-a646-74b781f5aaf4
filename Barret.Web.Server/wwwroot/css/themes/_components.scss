// Radzen Components Core Styling
// Essential component styles for Radzen themes
// This is a simplified version focusing on the most commonly used components

// CSS Custom Properties Generation
@each $name, $value in $rz-theme-colors-map {
  :root,
  .rz-#{$theme-name} {
    --rz-#{$name}: #{$value};
  }
}

// Additional CSS custom properties
:root,
.rz-#{$theme-name} {
  --rz-border-width: #{$rz-border-width};
  --rz-border-radius: #{$rz-border-radius};
  --rz-body-font-size: #{$rz-body-font-size};
  --rz-body-line-height: #{$rz-body-line-height};
  --rz-icon-size: #{$rz-icon-size};
  --rz-outline-offset: #{$rz-outline-offset};
  --rz-outline-width: #{$rz-outline-width};
  --rz-outline-color: var(--rz-primary);
  --rz-outline-normal: none;
  --rz-outline-focus: #{$rz-outline-width} solid var(--rz-outline-color);
  --rz-transition-all: #{$rz-transition-all};
  
  // Button variables
  --rz-button-base-background-color: var(--rz-primary);
  --rz-button-base-color: var(--rz-on-primary);
  --rz-button-border-radius: #{$button-border-radius};
  --rz-button-background-size: auto;
  --rz-button-shadow: none;
  --rz-button-transition: var(--rz-transition-all), width 0, height 0;
  --rz-button-line-height: 1.25rem;
  --rz-button-vertical-align: top;
  --rz-button-hover-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2);
  --rz-button-hover-gradient: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  --rz-button-hover-background-size: auto;
  --rz-button-focus-shadow: inset 0 -3px 0 0 rgba(255, 255, 255, 0.2);
  --rz-button-focus-gradient: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  --rz-button-focus-background-size: auto;
  --rz-button-focus-outline: var(--rz-outline-focus);
  --rz-button-focus-outline-offset: var(--rz-outline-offset);
  --rz-button-active-shadow: inset 0 3px 0 0 rgba(0, 0, 0, 0.1);
  --rz-button-active-gradient: linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1));
  --rz-button-active-background-size: auto;
  --rz-button-disabled-opacity: 0.2;
  --rz-button-empty-opacity: 0.4;
  
  // Grid variables
  --rz-grid-simple-filter-icon-active-color: #{$grid-simple-filter-icon-active-color};
  --rz-grid-simple-filter-icon-active-background-color: #{$grid-simple-filter-icon-active-background-color};
}

// Base body styling
@if not $base {
  body {
    background-color: var(--rz-body-background-color, var(--rz-base-100));
    color: var(--rz-text-color, var(--rz-on-base));
    font-family: var(--rz-font-family);
    font-size: var(--rz-body-font-size);
    line-height: var(--rz-body-line-height);
  }
}

// Essential component imports
// Note: In a full implementation, you would import individual component SCSS files here
// For this simplified version, we'll include basic button styling

// Button Component
%button, .rz-button {
  box-sizing: border-box;
  display: inline-block;
  margin: 0;
  color: var(--rz-button-base-color);
  background-color: var(--rz-button-base-background-color);
  background-size: var(--rz-button-background-size);
  background-repeat: no-repeat;
  border: none;
  border-radius: var(--rz-button-border-radius);
  outline: none;
  box-shadow: var(--rz-button-shadow);
  font-size: 1.0625rem;
  font-family: inherit;
  line-height: var(--rz-button-line-height);
  text-decoration: none;
  transition: var(--rz-button-transition);
  -webkit-appearance: none;
  cursor: pointer;
  padding: 0.5rem 1rem;

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-button-focus-outline);
    outline-offset: var(--rz-button-focus-outline-offset);
  }

  &:not(.rz-state-disabled) {
    &:not(:active) {
      @include rz-hover-state {
        text-decoration: none;
        background-image: var(--rz-button-hover-gradient);
        background-size: var(--rz-button-hover-background-size);
        box-shadow: var(--rz-button-hover-shadow);
      }
      
      &:focus-visible {
        text-decoration: none;
        background-image: var(--rz-button-focus-gradient);
        background-size: var(--rz-button-focus-background-size);
        box-shadow: var(--rz-button-focus-shadow);
      }
    }

    &:active {
      text-decoration: none;
      background-image: var(--rz-button-active-gradient);
      background-size: var(--rz-button-active-background-size);
      box-shadow: var(--rz-button-active-shadow);
    }
  }

  &.rz-state-disabled {
    opacity: var(--rz-button-disabled-opacity);
    cursor: initial;
  }

  .rz-button-box {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    vertical-align: var(--rz-button-vertical-align);
    line-height: var(--rz-button-line-height);
  }

  .rz-button-text {
    vertical-align: var(--rz-button-vertical-align);
  }

  .rzi {
    vertical-align: var(--rz-button-vertical-align);
  }
}

// Button variants
@each $style, $button in map-merge($severity-styles-map, $base-styles-map) {
  .rz-button.rz-#{$style} {
    @each $name, $value in $button {
      #{$name}: #{$value};
      @if $name == 'background-color' and $style != 'dark' and $style != 'light' {
        &.rz-shade-lighter { background-color: var(--rz-#{$style}-lighter); color: var(--rz-on-#{$style}-lighter); }
        &.rz-shade-light { background-color: var(--rz-#{$style}-light); color: var(--rz-on-#{$style}-light); }
        &.rz-shade-default { background-color: var(--rz-#{$style}); color: var(--rz-on-#{$style}); }
        &.rz-shade-dark { background-color: var(--rz-#{$style}-dark); color: var(--rz-on-#{$style}-dark); }
        &.rz-shade-darker { background-color: var(--rz-#{$style}-darker); color: var(--rz-on-#{$style}-darker); }
      }
    }
  }
}
