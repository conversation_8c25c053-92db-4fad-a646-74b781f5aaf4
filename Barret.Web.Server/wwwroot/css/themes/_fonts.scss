// Radzen Theme Fonts
// Font imports and definitions for Radzen themes

// Import Google Fonts
@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap');

// Font face definitions for local fonts
@font-face {
  font-family: 'Material Symbols Outlined';
  font-style: normal;
  font-weight: 100 700;
  font-display: block;
  src: url('../fonts/MaterialSymbolsOutlined.woff2') format('woff2');
}

@font-face {
  font-family: 'Roboto Flex';
  font-style: normal;
  font-weight: 100 1000;
  font-display: swap;
  src: url('../fonts/RobotoFlex.woff2') format('woff2');
}

@font-face {
  font-family: 'Source Sans 3';
  font-style: normal;
  font-weight: 200 900;
  font-display: swap;
  src: url('../fonts/SourceSans3VF-Upright.ttf.woff2') format('woff2');
}

@font-face {
  font-family: 'Source Sans 3';
  font-style: italic;
  font-weight: 200 900;
  font-display: swap;
  src: url('../fonts/SourceSans3VF-Italic.ttf.woff2') format('woff2');
}

// Font variable definitions
:root {
  --rz-font-family: #{$rz-text-font-family};
  --rz-font-size: #{$rz-body-font-size};
  --rz-line-height: #{$rz-body-line-height};
  --rz-icon-font-family: 'Material Symbols Outlined';
}

// Base font styling
body {
  font-family: var(--rz-font-family);
  font-size: var(--rz-font-size);
  line-height: var(--rz-line-height);
}

// Icon font class
.rzi {
  font-family: var(--rz-icon-font-family);
  font-weight: normal;
  font-style: normal;
  font-size: var(--rz-icon-size);
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
