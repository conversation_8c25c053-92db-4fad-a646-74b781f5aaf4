# Radzen SCSS Theme Compilation Workflow

This directory contains the SCSS source files for custom Radzen Blazor themes and the build pipeline to compile them into CSS.

## Architecture Overview

Our theming system uses a hybrid approach:
- **Radzen Themes**: Custom SCSS themes compiled to CSS for Radzen component styling
- **Tailwind CSS**: Utility classes for layout, spacing, and custom component styling
- **Custom CSS**: Component-specific overrides in `barret-theme.css`

## Directory Structure

```
wwwroot/css/themes/
├── _variables.scss      # Core Radzen variables and configuration
├── _mixins.scss         # SCSS mixins for component styling
├── _fonts.scss          # Font imports and definitions
├── _components.scss     # Core Radzen component styling
├── software.scss        # Light theme definition
├── software-dark.scss   # Dark theme definition
└── README.md           # This file
```

## Build Pipeline

### NPM Scripts

The build process is integrated into your existing npm workflow:

```bash
# Build all CSS (Tailwind + Radzen themes)
npm run build:css

# Development build with source maps
npm run build:css:dev

# Watch mode for development
npm run watch:css

# Build only Ra<PERSON>zen themes
npm run build:radzen

# Watch only Radzen themes
npm run watch:radzen
```

### MSBuild Integration

The SCSS compilation is automatically integrated into your .NET build process:

- **Build Target**: Compiles SCSS before each build
- **Watch Target**: Monitors SCSS files during development
- **Clean Target**: Removes compiled CSS files during clean

### Generated Files

The build process generates these files in `wwwroot/css/`:
- `software.css` - Compiled light theme
- `software-dark.css` - Compiled dark theme
- `*.css.map` - Source maps for debugging (development only)

## Theme Customization

### Creating a New Theme

1. Copy `software.scss` to `new-theme.scss`
2. Update the theme name: `$theme-name: new-theme;`
3. Modify color variables as needed
4. Add build scripts to `package.json`
5. Update MSBuild targets if needed

### Color Variables

Key color variables you can customize:

```scss
$rz-primary: #598087;      // Primary brand color
$rz-secondary: #80a4ab;    // Secondary color
$rz-base: #dadfe2;         // Base background color
$rz-info: #2cc8c8;         // Info color
$rz-success: #5dbf74;      // Success color
$rz-warning: #fac152;      // Warning color
$rz-danger: #f9777f;       // Danger/error color
```

### Dark Theme

For dark themes, set `$theme-dark: true` and adjust:
- Base colors (darker backgrounds)
- Text contrast colors
- Border colors for dark mode

## Development Workflow

### Local Development

1. Start the SCSS watch process:
   ```bash
   npm run watch:radzen
   ```

2. Start your Blazor application:
   ```bash
   dotnet run
   ```

3. Edit SCSS files - changes will be automatically compiled

### Production Build

The production build automatically compiles and minifies SCSS:

```bash
npm run build:css
dotnet build --configuration Release
```

## Integration with Existing Styles

### Load Order

The CSS files are loaded in this order for proper cascading:

1. Bootstrap CSS (if used)
2. **Custom Radzen Theme** (`software.css`)
3. Tailwind CSS (`dist.css`)
4. Custom overrides (`barret-theme.css`)

### Compatibility

- **Tailwind CSS**: Use for layout, spacing, and utility classes
- **Radzen Theme**: Provides component styling and color variables
- **Custom CSS**: Use for specific component overrides

## Troubleshooting

### Common Issues

1. **SCSS Compilation Errors**
   - Check syntax in SCSS files
   - Ensure all imports are correct
   - Verify npm dependencies are installed

2. **CSS Not Loading**
   - Check file paths in HTML
   - Verify files were generated in `wwwroot/css/`
   - Clear browser cache

3. **Build Failures**
   - Ensure Node.js and npm are installed
   - Run `npm install` to install dependencies
   - Check MSBuild output for errors

### Debugging

- Use source maps in development for debugging
- Check browser dev tools for CSS loading issues
- Verify CSS custom properties are being generated

## Best Practices

1. **Keep themes focused**: Only modify colors and basic styling
2. **Use CSS custom properties**: Leverage the generated CSS variables
3. **Test both themes**: Ensure light and dark themes work correctly
4. **Maintain consistency**: Follow the existing color naming conventions
5. **Document changes**: Update this README when adding new themes

## Implementation Status

✅ **COMPLETED FEATURES:**
- SCSS compilation integrated into .NET build pipeline
- Custom Radzen theme generation (light and dark variants)
- MSBuild targets for automatic compilation
- npm scripts for development and production builds
- CSS custom properties generation for all theme colors
- Font imports and icon font integration
- Essential Radzen component styling (buttons, forms, etc.)
- Source map generation for debugging
- Clean build targets for CSS file management

✅ **VERIFIED FUNCTIONALITY:**
- Build process: `dotnet build` automatically compiles SCSS
- Development workflow: `npm run watch:radzen` for live compilation
- Production builds: Minified CSS output
- Theme switching: CSS custom properties support
- Integration: Works with existing Tailwind CSS workflow

## Future Enhancements

Potential improvements to consider:
- WCAG-compliant color variants
- Additional theme variants (high contrast, etc.)
- Automated theme generation tools
- Integration with design tokens
- Modern SCSS syntax migration (replace @import with @use)
- Component-specific SCSS modules
- Theme validation and testing tools
