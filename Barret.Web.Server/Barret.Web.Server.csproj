<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>

    <PackageReference Include="FluentValidation" Version="11.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.2" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="8.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Radzen.Blazor" Version="7.0.7" />
    <PackageReference Include="ReactiveUI" Version="19.5.41" />
    <PackageReference Include="ReactiveUI.Blazor" Version="19.5.41" />
    <PackageReference Include="ReactiveUI.Fody" Version="19.5.41" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Barret.Core\Barret.Core.csproj" />
    <ProjectReference Include="..\Barret.Services.Core\Barret.Services.Core.csproj" />
    <ProjectReference Include="..\Barret.Services\Barret.Services.csproj" />
    <ProjectReference Include="..\Barret.Shared\Barret.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- Exclude the Test directory from compilation -->
    <Compile Remove="Test\**" />
    <Content Remove="Test\**" />
    <EmbeddedResource Remove="Test\**" />
    <None Remove="Test\**" />
  </ItemGroup>

  <!-- SCSS Compilation Targets -->
  <PropertyGroup>
    <EnableSassCompilation Condition="'$(EnableSassCompilation)' == ''">true</EnableSassCompilation>
    <SassInputPath>wwwroot/css/themes</SassInputPath>
    <SassOutputPath>wwwroot/css</SassOutputPath>
  </PropertyGroup>

  <!-- SCSS Files to watch for changes -->
  <ItemGroup>
    <SassFiles Include="$(SassInputPath)/**/*.scss" />
    <Watch Include="$(SassInputPath)/**/*.scss" />
  </ItemGroup>

  <!-- Build Target: Compile SCSS before build -->
  <Target Name="CompileScss" BeforeTargets="Build" Condition="'$(EnableSassCompilation)' == 'true'">
    <Message Text="Compiling SCSS files..." Importance="high" />
    <Exec Command="npm run build:radzen"
          ContinueOnError="false"
          WorkingDirectory="$(MSBuildProjectDirectory)/../" />
  </Target>

  <!-- Development Target: Watch SCSS files during development -->
  <Target Name="WatchScss" Condition="'$(DOTNET_RUNNING_IN_CONTAINER)' != 'true' AND '$(EnableSassCompilation)' == 'true'">
    <Message Text="Starting SCSS watch mode..." Importance="high" />
    <Exec Command="npm run watch:radzen"
          ContinueOnError="true"
          WorkingDirectory="$(MSBuildProjectDirectory)/../" />
  </Target>

  <!-- Clean Target: Remove compiled CSS files -->
  <Target Name="CleanScss" BeforeTargets="Clean">
    <ItemGroup>
      <CompiledCssFiles Include="$(SassOutputPath)/software.css" />
      <CompiledCssFiles Include="$(SassOutputPath)/software-dark.css" />
      <CompiledCssFiles Include="$(SassOutputPath)/software.css.map" />
      <CompiledCssFiles Include="$(SassOutputPath)/software-dark.css.map" />
    </ItemGroup>
    <Delete Files="@(CompiledCssFiles)" ContinueOnError="true" />
  </Target>

</Project>
